<template>
    <div class="right-scroll">
        <div class="header-bar">
            <div class="header-left">
                <div class="table-title">{{ selectedTableDetail.templateName }}</div>
            </div>
            <div class="header-controls">
                <el-button @click="$emit('closeDetail')" type="primary">返回</el-button>
            </div>
        </div>

        <div class="right-content" style="height: calc(100vh - 320px)">
            <template v-if="selectedItem.batchNumber">
                <!-- 录入信息标题 -->
                <div class="section-title">
                    <span>批次信息</span>
                </div>
                <div class="approval-history">
                    <div class="history-item">
                        <span class="history-label">批次：</span>
                        <span class="history-value">
                            {{ dayjs(selectedItem.batchNumber).format("YYYY年MM月") }}
                        </span>
                    </div>
                </div>
            </template>

            <template v-if="recordList && recordList.length > 0">
                <div class="section-title">
                    <span>审核历史</span>
                </div>
                <div class="approval-history">
                    <div class="history-item">
                        <span class="history-label">审核人：</span>
                        <span class="history-value">
                            {{
                                getPersonName(
                                    allPersonListWithDisabled,
                                    String(recordList[recordList.length - 1]?.createdBy)
                                )
                            }}
                        </span>
                    </div>
                    <div class="history-item">
                        <span class="history-label">审核通过时间：</span>
                        <span class="history-value">
                            {{ dayjs(recordList[recordList.length - 1]?.createdAt).format("YYYY-MM-DD") }}
                        </span>
                    </div>
                </div>
            </template>

            <div class="section-title">
                <span>录入信息</span>
            </div>
            <div class="fields-container">
                <!-- 过滤掉文件类型的字段，只显示非文件字段 -->
                <div v-for="v in nonFileFieldList" :key="v.id" class="field-card">
                    <!-- 字段头部 -->
                    <div class="field-header" :class="{ star: v.isRequired === 1 }">
                        <div class="field-header-content">
                            <!-- 字段名称 -->
                            <div
                                class="field-name"
                                :class="{ 'is-category-public-field': v.categoryPublicType }">
                                {{ v.value }}
                            </div>
                            <!-- 信息图标 -->
                            <div v-if="v.description" class="info-icon">
                                <a-tooltip placement="right" color="#fff">
                                    <template #title>
                                        <div
                                            style="
                                                color: #000;
                                                font-size: 12px;
                                                padding: 5px;
                                                letter-spacing: 0.1rem;
                                            ">
                                            {{ v.description }}
                                        </div>
                                    </template>
                                    <div class="info-circle"></div>
                                </a-tooltip>
                            </div>
                        </div>
                    </div>
                    <div class="field-content">
                        <div class="inputs">
                            <div
                                class="box"
                                v-if="
                                    v.type === categoryTemplateValueType_NEW.STRING ||
                                    v.type === categoryTemplateValueType_NEW.INTEGER ||
                                    v.type === categoryTemplateValueType_NEW.DOUBLE ||
                                    v.type === categoryTemplateValueType_NEW.PROJECT_NAME ||
                                    v.type === categoryTemplateValueType_NEW.STUDENT
                                ">
                                <span v-if="fieldValueMap[v.id]?.fieldValue?.value">
                                    {{ fieldValueMap[v.id]?.fieldValue?.value }}
                                </span>
                                <span v-else>用户未填写此项</span>
                            </div>
                            <div
                                class="box"
                                v-if="
                                    v.type === categoryTemplateValueType_NEW.DATE ||
                                    v.type === categoryTemplateValueType_NEW.PROJECT_DATE
                                ">
                                <el-date-picker
                                    disabled
                                    :model-value="fieldValueMap[v.id]?.fieldValue?.value"
                                    type="date"
                                    placeholder="用户未填写此项" />
                            </div>
                            <div class="box" v-if="v.type === categoryTemplateValueType_NEW.PERSON_SINGLE">
                                <el-input
                                    disabled
                                    placeholder="用户未填写此项"
                                    :model-value="
                                        getPersonName(
                                            allPersonListWithDisabled,
                                            fieldValueMap[v.id]?.fieldValue?.value
                                        )
                                    " />
                                <div style="color: #23346d; margin-left: 30px; width: 120px">是否本院</div>
                                <el-switch
                                    :model-value="isInternalPerson(fieldValueMap[v.id]?.fieldValue?.value)"
                                    disabled />
                            </div>
                            <div
                                class="multi-person"
                                v-if="
                                    v.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                                    v.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                                    v.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                                ">
                                <div v-for="item in fieldValueMap[v.id]?.fieldValue?.value.split(',')">
                                    <el-input
                                        disabled
                                        placeholder="用户未填写此项"
                                        :model-value="getPersonName(allPersonListWithDisabled, item)" />
                                    <div
                                        style="
                                            color: #23346d;
                                            margin-left: 30px;
                                            width: 120px;
                                            display: flex;
                                            align-items: center;
                                        ">
                                        是否本院
                                    </div>
                                    <el-switch :model-value="isInternalPerson(item)" disabled />
                                </div>
                            </div>
                            <div
                                class="box"
                                v-if="
                                    v.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                                    v.type === categoryTemplateValueType_NEW.LEVEL ||
                                    v.type === categoryTemplateValueType_NEW.PROJECT_STATUS
                                ">
                                <el-select
                                    disabled
                                    :model-value="
                                        getSingleSelectLabel(
                                            v.dictList,
                                            fieldValueMap[v.id]?.fieldValue?.value
                                        )
                                    "
                                    style="width: 300px"
                                    placeholder="用户未填写此项">
                                    <el-option
                                        v-for="item in v.dictList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.name" />
                                </el-select>
                            </div>
                            <div class="box" v-if="v.type === categoryTemplateValueType_NEW.ENUM_MULTI">
                                <el-select
                                    multiple
                                    disabled
                                    :model-value="
                                        getMultiSelectLabels(
                                            v.dictList,
                                            fieldValueMap[v.id]?.fieldValue?.value
                                        )
                                    "
                                    style="width: 300px"
                                    placeholder="用户未填写此项">
                                    <el-option
                                        v-for="item in v.dictList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.name" />
                                </el-select>
                            </div>
                            <div class="box" v-if="v.type === categoryTemplateValueType_NEW.ISN">
                                <el-radio-group
                                    disabled
                                    :model-value="Number(fieldValueMap[v.id]?.fieldValue?.value)">
                                    <el-radio :value="1" size="large">是</el-radio>
                                    <el-radio :value="0" size="large">否</el-radio>
                                </el-radio-group>
                                <div
                                    v-if="!fieldValueMap[v.id]?.fieldValue?.value"
                                    style="font-size: 14px; color: #8a8886">
                                    用户未填写此项
                                </div>
                            </div>
                            <div class="box" v-if="v.type === categoryTemplateValueType_NEW.MONEY">
                                <el-input
                                    v-if="fieldValueMap[v.id]?.fieldValue?.value"
                                    disabled
                                    :model-value="fieldValueMap[v.id]?.fieldValue?.value"
                                    :formatter="
                                        (value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                                    "
                                    :parser="(value) => value.replace(/\￥\s?|(,*)/g, '')" />
                                <el-input v-else disabled placeholder="用户未填写此项" />
                            </div>
                            <div v-if="v.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR">
                                <el-select
                                    multiple
                                    :model-value="
                                        getMultiSelectLabels(
                                            allDependentMajors,
                                            fieldValueMap[v.id]?.fieldValue?.value
                                        )
                                    "
                                    disabled
                                    style="width: 100%; min-width: 300px"
                                    placeholder="用户未填写此项">
                                    <el-option
                                        v-for="item in allDependentMajors"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id" />
                                </el-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 佐证材料区域 -->
            <template v-if="supportingMaterialsList.length > 0 && hasBackendPermission">
                <div class="section-title">
                    <span>佐证材料</span>
                </div>
                <div class="supporting-materials-container">
                    <FilesReview :files="supportingMaterialsList" />
                </div>
            </template>

            <PerformanceScore
                v-if="
                    hasAdminEntryPermission &&
                    selectedTableDetail?.templateType != categoryTemplateType['院情数据']
                "
                :detail-and-score="detailAndScore"
                :is-already-passed="true" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { categoryTemplateValueType_NEW } from "@/enums/categoryTemplate/categoryTemplateValueType";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { CmsCategoryTemplate_, CmsCategoryTemplateValue10, CmsCheck_, CmsTagDetailAndScoreDto } from "@/apis/types";
import { getPersonName } from "@/utils/getNames";
import { dayjs } from "element-plus";
import PerformanceScore from "@/components/PerformanceScore/index.vue";
import FilesReview from "@/components/FilesReview/index.vue";

interface Props {
    selectedItem: any;
    selectedTableDetail: CmsCategoryTemplate_;
    fieldList: CmsCategoryTemplateValue10[];
    recordList: CmsCheck_[];
    detailAndScore: CmsTagDetailAndScoreDto;
    allPersonListWithDisabled: any[];
    allDependentMajors: any[];
    hasBackendPermission: boolean;
    hasAdminEntryPermission: boolean;
}

interface Emits {
    (e: 'closeDetail'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 根据选中的项目生成映射表
const fieldValueMap = computed(() => createFieldValueMap(props.fieldList, props.selectedItem.values));

// 过滤出非文件类型的字段列表
const nonFileFieldList = computed(() => {
    return props.fieldList.filter((field) => field.type !== categoryTemplateValueType_NEW.FILE);
});

// 生成佐证材料列表字符串
const supportingMaterialsList = computed(() => {
    if (!fieldValueMap.value || !props.hasBackendPermission) return "";

    const fileList: string[] = [];

    // 遍历所有文件类型的字段
    props.fieldList.forEach((field) => {
        if (field.type === categoryTemplateValueType_NEW.FILE) {
            const fieldData = fieldValueMap.value[field.id];
            if (fieldData?.fieldValue?.value) {
                const fileUrls = fieldData.fieldValue.value.split(",");
                fileUrls.forEach((fileUrl) => {
                    if (fileUrl.trim()) {
                        fileList.push(fileUrl);
                    }
                });
            }
        }
    });

    return fileList.join(",");
});

// 判断是否为院内人员
const isInternalPerson = (value: string) => {
    if (!value) return false;
    return /^\d+$/.test(value);
};

// 获取多选下拉框的label
const getMultiSelectLabels = (dictList: any[], value: string) => {
    if (!value) return [];
    const ids = value.split(",");
    if (Number.isNaN(Number(ids[0]))) {
        return value;
    }
    return ids.map((id) => {
        const item = dictList.find((dict) => dict.id.toString() === id);
        // 为依托专业情况设置特殊返回值
        if (item.hasOwnProperty("organizationName")) {
            return item.organizationName;
        } else {
            return item ? item.name : "";
        }
    });
};

// 获取单选下拉框的label
const getSingleSelectLabel = (dictList: any[], value: string) => {
    if (!value) return "";
    const item = dictList.find((dict) => dict.id.toString() === value);
    return item ? item.name : "";
};

/**
 * 根据 fieldsList 和 values 生成映射表
 */
const createFieldValueMap = (fieldsList: any[], values: any[]) => {
    return fieldsList.reduce(
        (acc, field) => {
            const matchedValue = values.find((v) => v.categoryTemplateValueId === field.id);
            acc[field.id] = {
                fieldProperties: field,
                fieldValue: matchedValue ? matchedValue : null,
            };
            return acc;
        },
        {} as Record<string, any>
    );
};
</script>

<style scoped lang="scss">
@use "@/assets/styles/templateFieldsDetail.scss";

.multi-person {
    display: flex;
    flex-direction: column;
    div {
        display: flex;
        margin: 5px 0;
    }
}

.approval-history {
    display: flex;
    padding: 32px 40px;
    gap: 16px;
    background: #f9fbff;
    border-radius: 8px;
    flex-direction: column;

    .history-item {
        .history-label {
            font-weight: 500;
        }

        .history-value {
            font-weight: 400;
        }
    }

    .no-data {
        color: #909399;
        text-align: center;
        line-height: 60px;
        font-size: 14px;
    }
}

// 佐证材料区域样式
.supporting-materials-container {
    padding: 20px;
    background: #f9fbff;
    border-radius: 8px;
    margin-bottom: 20px;
}
</style>