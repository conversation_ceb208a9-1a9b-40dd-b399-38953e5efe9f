<template>
    <div class="app-main">
        <Header class="app-main-header" />
        <div class="app-main-content" :class="{ 'no-child-route': isNoChildRoute }">
            <Navbar v-if="routeList.length > 0" />
            <div class="router-main" :class="{ 'has-navbar': routeList.length > 0, 'no-child-route': isNoChildRoute }">
                <router-view v-slot="{ Component, route }">
                    <transition name="fade-transform" mode="out-in">
                        <component :is="Component" :key="route.path" />
                    </transition>
                </router-view>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Navbar from "./Navbar.vue";
import { RouteLocationMatched, useRoute, useRouter } from "vue-router";
import Header from "./Header.vue";

const route = useRoute();
const router = useRouter();

// 当前导航菜单列表
const routeList = ref<RouteLocationMatched[]>([]);

// 获取父级路由
const getParentRoute = (matched: RouteLocationMatched[]): RouteLocationMatched | undefined => {
    return matched.find((route) => {
        const segments = route.path.split("/").filter(Boolean);
        return segments.length === 2;
    });
};

// 判断当前是否为无子路由页面
const isNoChildRoute = computed(() => {
    return routeList.value.length === 0;
});


// 监听路由变化，更新导航菜单
watch(
    () => route.matched,
    (matched) => {
        const parentRoute = getParentRoute(matched);
        routeList.value = (parentRoute?.children as RouteLocationMatched[]) || [];
    },
    { immediate: true },
);
</script>

<style lang="scss" scoped>
@use "../../assets/styles/mixins.scss" as mix;
// fix css style bug in open el-dialog
.app-main {
    height: 100vh;
    display: flex;
    flex-direction: column;
    /* overflow: hidden; */
    .app-main-header {
        height: 64px;
    }
    .app-main-content {
        flex: 1;
        height: 0;
        display: flex;
        flex-direction: column;
        padding: 64px 52px 0 52px;
        &.no-child-route {
            padding: 24px 52px 0 52px;
        }
        .router-main {
            flex: 1;
            width: 100%;
            height: 0;
            margin-top: 25px;
            &.no-child-route {
                margin-top: 0;
            }
            // 根据是否有子路由使用不同的高度计算
            /* @include mix.custom-scrollBar-height-no-navbar;

      &.has-navbar {
        @include mix.custom-scrollBar-height;
      } */
            overflow: auto;
        }
    }
}
</style>
