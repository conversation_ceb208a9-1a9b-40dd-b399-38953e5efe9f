<template>
    <div class="wrapper">
        <div class="table">
            <div class="left">
                <div class="left-scroll">
                    <div class="search-box">
                        <el-input placeholder="输入数据表名称查询" clearable v-model="searchValue" prefix-icon="Search">
                        </el-input>
                    </div>
                    <div class="left-title">数据表</div>
                    <div class="left-list">
                        <el-scrollbar ref="leftScrollbar" height="100%">
                            <div
                                style="display: flex; justify-content: space-between"
                                class="template"
                                :class="{ active: currentIndex === i }"
                                v-for="(v, i) in filteredTemplateList"
                                @click="loadDeatil(v.id, i, true)"
                                :key="v.id">
                                <div class="template-name" :title="v.templateName">
                                    {{ v.templateName }}
                                </div>
                                <!-- 有三种类型，院情、工作、成果，根据三种不同类型添加不同样式 -->
                                <el-tag
                                    :class="{
                                        'type-1': v.templateType === categoryTemplateType['院情数据'],
                                        'type-2': v.templateType === categoryTemplateType['工作数据'],
                                        'type-3': v.templateType === categoryTemplateType['成果数据'],
                                    }"
                                    >{{ categoryTemplateType[v.templateType].substring(0, 2) }}</el-tag
                                >
                                <el-popover popper-class="edit-popover" placement="right" width="70">
                                    <template #reference>
                                        <div>
                                            <MoreOutlined :rotate="180" />
                                        </div>
                                    </template>
                                    <div
                                        style="
                                            display: flex;
                                            justify-content: center;
                                            flex-direction: column;
                                            align-items: center;
                                            gap: 10px;
                                        ">
                                        <el-button text @click="editTemplate(v)"> 编辑 </el-button>
                                        <el-button
                                            :disabled="v.enabled === 1"
                                            style="margin-left: 0px"
                                            text
                                            type="danger"
                                            @click="deleteTemplate(v.id)">
                                            删除
                                        </el-button>
                                    </div>
                                </el-popover>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
                <div class="left-bottom">
                    <el-button type="primary" style="letter-spacing: 0.1rem" @click="addDialogFormVisible = true">
                        + 新增自定义表</el-button
                    >
                </div>
            </div>
            <div class="right" :loading="loading" ref="scrollContainer">
                <div v-if="templateDetail?.id" class="right-scroll">
                    <!-- 顶部标题栏 -->
                    <div class="header-bar">
                        <div class="header-left">
                            <div class="table-title">{{ templateDetail.templateName }}</div>
                            <el-button
                                v-if="templateDetail.templateType != categoryTemplateType['院情数据']"
                                @click="showPerformanceRules(templateDetail.id)"
                                type="primary"
                                >查看已关联绩效规则</el-button
                            >
                        </div>
                        <div class="header-controls">
                            <el-button v-if="!isEdit" type="primary" @click="switchEdit(true)">编辑</el-button>
                            <el-button v-if="isEdit" type="primary" @click="switchEdit(false)">保存</el-button>
                        </div>
                    </div>

                    <div class="right-content">
                        <!-- 录入信息标题 -->
                        <div class="section-title">
                            <span>录入信息</span>
                        </div>

                        <!-- 字段列表容器 -->
                        <div class="fields-container">
                            <VueDraggable
                                ghostClass="ghost"
                                :animation="150"
                                handle=".handle"
                                v-model="fieldsList"
                                :scroll="true"
                                :scrollSensitivity="100"
                                :scrollSpeed="20"
                                :forceFallback="true"
                                :scrollEl="scrollContainer">
                                <div
                                    v-if="fieldsList.length > 0"
                                    v-for="v in fieldsList"
                                    :key="v.id"
                                    class="field-card"
                                    :data-item-id="v.id">
                                    <!-- 字段头部 -->
                                    <div class="field-header" :class="{ star: v.isRequired === 1, handle: isEdit }">
                                        <div class="field-header-content">
                                            <!-- 字段名称 -->
                                            <div
                                                class="field-name"
                                                :class="{
                                                    'is-category-public-field': v.categoryPublicType,
                                                }">
                                                {{ v.value }}
                                            </div>
                                            <!-- 信息图标 -->
                                            <div v-if="v.description" class="info-icon">
                                                <a-tooltip placement="right" color="#fff">
                                                    <template #title>
                                                        <div
                                                            style="
                                                                color: #000;
                                                                font-size: 12px;
                                                                padding: 5px;
                                                                letter-spacing: 0.1rem;
                                                            ">
                                                            {{ v.description }}
                                                        </div>
                                                    </template>
                                                    <div class="info-circle"></div>
                                                </a-tooltip>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 字段内容 -->
                                    <div class="field-content">
                                        <div class="inputs">
                                            <div
                                                v-if="
                                                    v.type === categoryTemplateValueType_NEW.STRING ||
                                                    v.type === categoryTemplateValueType_NEW.INTEGER ||
                                                    v.type === categoryTemplateValueType_NEW.DOUBLE ||
                                                    v.type === categoryTemplateValueType_NEW.MONEY ||
                                                    v.type === categoryTemplateValueType_NEW.PROJECT_NAME ||
                                                    v.type === categoryTemplateValueType_NEW.STUDENT
                                                ">
                                                <el-input
                                                    style="width: 600px"
                                                    disabled
                                                    placeholder="录入者会在这里输入文本" />
                                            </div>
                                            <div
                                                v-if="
                                                    v.type === categoryTemplateValueType_NEW.DATE ||
                                                    v.type === categoryTemplateValueType_NEW.PROJECT_DATE
                                                ">
                                                <el-date-picker
                                                    disabled
                                                    type="date"
                                                    placeholder="录入者会在这里选择日期" />
                                            </div>
                                            <div v-if="v.type === categoryTemplateValueType_NEW.FILE">
                                                <el-upload disabled class="upload-demo">
                                                    <el-button disabled type="primary">
                                                        文件上传<el-icon class="el-icon--right"><Upload /></el-icon>
                                                    </el-button>
                                                </el-upload>
                                                <span class="desc">用户会在此处上传文件</span>
                                            </div>
                                            <div v-if="v.type === categoryTemplateValueType_NEW.ISN">
                                                <el-radio-group disabled>
                                                    <el-radio :value="1">是</el-radio>
                                                    <el-radio :value="0">否</el-radio>
                                                </el-radio-group>
                                                <div class="desc">用户会在此处选择</div>
                                            </div>
                                            <div v-if="v.type === categoryTemplateValueType_NEW.PERSON_SINGLE">
                                                <PersonSelector
                                                    :is-disabled="true"
                                                    :is-filterable="false"
                                                    :out-placeholder="'录入者会在这里选择人员（单个）'"
                                                    :person-list="[]" />
                                            </div>
                                            <div
                                                v-if="
                                                    v.type === categoryTemplateValueType_NEW.PERSON_MULTI ||
                                                    v.type === categoryTemplateValueType_NEW.PROJECT_PARTICIPATE ||
                                                    v.type === categoryTemplateValueType_NEW.PROJECT_MAIN
                                                ">
                                                <div style="z-index: 0; position: relative">
                                                    <MultiPersonSelector
                                                        :is-disabled="true"
                                                        :is-filterable="false"
                                                        :out-placeholder="'录入者会在这里选择人员（多个）'"
                                                        :person-list="[]" />
                                                </div>
                                            </div>
                                            <div v-if="v.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR">
                                                <el-select
                                                    style="width: 300px"
                                                    placeholder="录入者会在这里选择依托专业（多选）">
                                                    <el-option
                                                        v-for="item in activeDependentMajors"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id" />
                                                </el-select>
                                            </div>
                                            <div
                                                v-if="
                                                    v.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
                                                    v.type === categoryTemplateValueType_NEW.LEVEL ||
                                                    v.type === categoryTemplateValueType_NEW.PROJECT_STATUS
                                                ">
                                                <el-select
                                                    style="width: 300px"
                                                    placeholder="录入者会在这里选择（单选）">
                                                    <el-option
                                                        v-for="item in v.type === categoryTemplateValueType_NEW.LEVEL
                                                            ? getSortedLevelOptions(v.dictList)
                                                            : v.dictList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id" />
                                                </el-select>
                                            </div>
                                            <div v-if="v.type === categoryTemplateValueType_NEW.ENUM_MULTI">
                                                <el-select
                                                    style="width: 300px"
                                                    placeholder="录入者会在这里选择（多选）">
                                                    <el-option
                                                        v-for="item in v.dictList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id" />
                                                </el-select>
                                            </div>
                                        </div>
                                        <!-- 操作按钮 -->
                                        <transition name="slide-fade">
                                            <div v-if="isEdit" class="action-buttons">
                                                <div @click="moveItemUp(v)">
                                                    <ArrowUpOutlined />
                                                </div>
                                                <div @click="moveItemDown(v)">
                                                    <ArrowDownOutlined />
                                                </div>
                                                <div @click="handleEditValue(v)">
                                                    <EditOutlined />
                                                </div>
                                                <div @click="handleDeleteValue(v)">
                                                    <DeleteOutlined />
                                                </div>
                                            </div>
                                        </transition>
                                    </div>
                                </div>
                                <div v-else>
                                    <el-empty description="暂无数据项" />
                                </div>
                            </VueDraggable>
                        </div>

                        <!-- 新增数据项按钮 -->
                        <transition name="slide-fade">
                            <div v-if="isEdit">
                                <el-button @click="addValueFormVisible = true" type="primary">
                                    <template #icon>
                                        <PlusOutlined />
                                    </template>
                                    新增数据项
                                </el-button>
                            </div>
                        </transition>
                    </div>
                </div>
                <div v-else>
                    <el-empty description="请在左侧选择数据表" />
                </div>
            </div>
        </div>
        <!-- =========================== 新增/修改数据表弹窗 =========================== -->
        <el-dialog v-model="addDialogFormVisible" :title="addTemplateForm.id ? '修改数据表' : '新增数据表'" width="500">
            <el-form :model="addTemplateForm" :rules="addTemplateRules" ref="addTemplateFormRef">
                <el-form-item label="数据表名称" prop="templateName">
                    <el-input placeholder="请输入表名称" v-model="addTemplateForm.templateName" />
                </el-form-item>
                <el-form-item label="数据表类型" prop="templateType">
                    <el-select
                        :disabled="addTemplateForm.enabled === 1"
                        v-model="addTemplateForm.templateType"
                        placeholder="请选择">
                        <el-option
                            v-for="(v, i) in categoryTemplateTypeOptions"
                            :key="i"
                            :label="v.label"
                            :value="v.value" />
                    </el-select>
                </el-form-item>
                <!-- 数据表类型为院情数据时候，也有审核负责人，改名为数据表负责人 -->
                <el-form-item
                    :label="
                        addTemplateForm.templateType === categoryTemplateType['院情数据']
                            ? '数据表负责人'
                            : '审核负责人'
                    "
                    prop="checkId">
                    <el-select filterable placeholder="请选择" v-model="addTemplateForm.checkId">
                        <el-option v-for="v in checkPersonOptions" :key="v.id" :label="v.employeeName" :value="v.id" />
                    </el-select>
                </el-form-item>
                <el-form-item
                    :required="addTemplateForm.templateType === categoryTemplateType['院情数据']"
                    v-if="addTemplateForm.templateType === categoryTemplateType['院情数据']"
                    label="是否有批次号"
                    prop="batchType">
                    <el-radio-group v-model="addTemplateForm.batchType">
                        <el-radio :value="1">是</el-radio>
                        <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="数据表分类" prop="classify">
                    <el-select v-model="addTemplateForm.classify" placeholder="请选择">
                        <el-option v-for="(v, i) in classifyList" :key="v.id" :label="v.name" :value="v.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否仅秘书录入" prop="isSecretary">
                    <el-radio-group v-model="addTemplateForm.isSecretary">
                        <el-radio :value="1">是</el-radio>
                        <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="addDialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitAddTemplateForm(addTemplateFormRef)"> 确定 </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- =========================== 新增/修改数据表弹窗 =========================== -->

        <!-- =========================== 新增/修改数据项弹窗 =========================== -->
        <el-dialog
            v-model="addValueFormVisible"
            :title="addValueForm.id ? '编辑数据项' : '新增数据项'"
            width="500"
            :close-on-click-modal="false"
            @close="
                addValueFormVisible = false;
                nowDialogType = null;
            ">
            <el-form :model="addValueForm" ref="addValueFormRef" :rules="addValueRules">
                <el-form-item label="数据项名称" prop="value">
                    <el-input placeholder="实例数据项：项目名称" v-model="addValueForm.value" />
                </el-form-item>
                <el-form-item label="数据项类型" prop="type">
                    <el-cascader
                        popper-class="table-management-cascader"
                        :props="{ expandTrigger: 'hover', emitPath: false }"
                        style="width: 100%"
                        :disabled="isLockField(true)"
                        :show-all-levels="false"
                        @change="handleCategoryTemplateValueTypeChange"
                        placeholder="请选择"
                        v-model="addValueForm.type"
                        :options="categoryTemplateValueTypeOptions_Cascader_Ref">
                        <template #default="{ data }">
                            <el-tooltip
                                :hide-after="0"
                                popper-class="cascader-tooltip"
                                raw-content
                                v-if="data.desc"
                                :offset="30"
                                effect="customized"
                                :content="data.desc"
                                placement="right-start">
                                <div style="width: 100%; height: 100%">{{ data.label }}</div>
                            </el-tooltip>
                            <div v-else>{{ data.label }}</div>
                        </template>
                    </el-cascader>
                </el-form-item>
                <el-form-item label="是否必填" prop="isRequired">
                    <el-radio-group v-model="addValueForm.isRequired" :disabled="isLockField()">
                        <el-radio :value="1">是</el-radio>
                        <el-radio :value="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="description">
                    <el-input type="textarea" placeholder="备注" v-model="addValueForm.description" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="addValueFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitAddValueForm(addValueFormRef)"> 确定 </el-button>
                </div>
            </template>
        </el-dialog>
        <!-- =========================== 新增/修改数据项弹窗 =========================== -->

        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->
        <PerformanceRulesDialog
            v-model="performanceRulesDialogVisible"
            :performance-rules-list="performanceRulesList" />
        <!-- =========================== 查看已关联绩效规则弹窗 =========================== -->

        <!-- 开关型菜单 -->
        <Transition name="fade">
            <div v-if="nowDialogType === secondDialogType.SWITCH" class="my-dialog">
                <div class="dropdown-settings">
                    <div class="settings-title">管理下拉菜单选项</div>
                    <div class="settings-subTitle">在此配置您的下拉菜单选项</div>
                    <div class="settings-content">
                        <div v-for="(option, index) in dynamicOptions" :key="index" class="option-item flex-between">
                            <div class="option-label">{{ option.label }}</div>
                            <el-switch
                                :disabled="option.useCount && option.useCount > 0"
                                :active-value="1"
                                :inactive-value="0"
                                v-model="option.enabled"
                                style="--el-switch-on-color: #586fbb" />
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
        <!-- 预览型菜单 -->
        <Transition name="fade">
            <div v-if="nowDialogType === secondDialogType.OVER_VIEW" class="my-dialog">
                <div class="dropdown-settings">
                    <div class="settings-title">查看预设下拉菜单选项</div>
                    <div class="settings-subTitle">在此预览您的下拉菜单选项</div>
                    <div class="settings-content">
                        <div v-for="(option, index) in dynamicOptions" :key="index" class="option-item">
                            <div class="option-label">{{ option.label }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
        <!-- 自定义型菜单 -->
        <Transition name="fade">
            <div v-if="nowDialogType === secondDialogType.CUSTOMIZE" class="my-dialog">
                <div class="dropdown-settings">
                    <div class="settings-title">管理下拉菜单选项</div>
                    <div class="settings-subTitle">在此配置您的下拉菜单选项</div>
                    <div
                        style="
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            height: 50px;
                            color: #23346d;
                            position: sticky;
                            top: 0;
                            background-color: white;
                            z-index: 10;
                            padding-bottom: 10px;
                            margin-bottom: 10px;
                            border-bottom: 1px solid #eff3f9;
                        ">
                        <div>选项设置</div>
                        <div>
                            <el-button @click="addCustomizeOption" circle color="#586FBB" :icon="Plus"></el-button>
                        </div>
                    </div>
                    <div class="settings-content">
                        <div v-for="(option, index) in customizeOptions" :key="index" class="option-item">
                            <div class="option-label">
                                <div @click="option.useCount && option.useCount > 0 ? null : editOption(index)">
                                    <!-- 给这个input添加键盘回车事件 -->
                                    <el-input
                                        :ref="setInputRef(index)"
                                        autofocus
                                        @keyup.enter="finishEditing(index)"
                                        style="width: 250px"
                                        @blur="finishEditing(index)"
                                        v-if="option.isEdit"
                                        v-model="option.label"
                                        placeholder="新选项" />
                                    <div :title="option.label" class="option-value" v-else>
                                        {{ option.label == "" ? "新选项" : option.label }}
                                    </div>
                                </div>
                                <div>
                                    <el-button
                                        v-if="!option.isEdit"
                                        :disabled="option.useCount && option.useCount > 0"
                                        @click="editOption(index)"
                                        size="small"
                                        color="#586FBB"
                                        :icon="Edit"></el-button>
                                    <el-button
                                        :disabled="option.useCount && option.useCount > 0"
                                        @click="deleteCustomizeOption(index)"
                                        size="small"
                                        type="danger"
                                        :icon="Delete" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>

        <!-- 类型描述面板 -->
        <Transition name="fade">
            <div v-if="nowDialogType === secondDialogType.TYPE_DESCRIPTION" class="my-dialog type-description-dialog">
                <div class="type-description-title">数据项类型信息</div>
                <div class="description-text" v-html="currentTypeDescriptionHtml || '暂无说明'"></div>
            </div>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import { Check, Upload, Delete, Edit, Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";
import { categoryTemplateType } from "@/enums/categoryTemplate/categoryTemplateType";
import { enumToOptions, enumToOptionsGeneric, useDynamicDropdownOptions } from "@/utils/enums";
import { PermissionEnum, PermissionIds } from "@/enums/roles/authorCards";
import {
    categoryTemplateValueType_NEW,
    categoryTemplateValueTypeOptions_Cascader_NEW,
} from "@/enums/categoryTemplate/categoryTemplateValueType";
import { publicPerformanceType, publicPerformanceTypeMap } from "@/models/publicPerformanceType";
import MultiPersonSelector from "@/components/MultiPersonSelector/index.vue";
import { VueDraggable } from "vue-draggable-plus";
import { gradeOptions, gradeOptionsMap } from "@/enums/options/gradeOptions";
import { umsPersonAuditList } from "@/apis/umsPersonController";
import {
    cmsCategoryTemplateCreate,
    cmsCategoryTemplateDelete,
    cmsCategoryTemplateGetById,
    cmsCategoryTemplateUpdate,
} from "@/apis/cmsCategoryTemplateController";
import {
    cmsCategoryTemplateValueCreate,
    cmsCategoryTemplateValueDelete,
    cmsCategoryTemplateValueGetById,
    cmsCategoryTemplateValueGetValueByTemplateId,
    cmsCategoryTemplateValueUpdateList,
    cmsCategoryTemplateValueUpdate,
} from "@/apis/cmsCategoryTemplateValueController";
import {
    CmsCategoryTemplate0,
    CmsCategoryTemplateValue10,
    CmsCategoryTemplateValue_,
    CmsTag_,
    UmsPerson_,
} from "@/apis/types";
import { cmsClassifyList } from "@/apis/cmsClassifyController";
import { CmsClassify_ } from "@/apis/types";
import { cmsTagCategoryEntityGetTagTreeListByEntityId } from "@/apis/cmsTagCategoryEntityController";
import PerformanceRulesDialog from "@/components/PerformanceRulesDialog";
import { RelyingMajorOptions, RelyingMajorOptionsMap } from "@/enums/options/RelyingMajorOptions";
import { ProjectStatus, ProjectStatusMap } from "@/enums/project/projectStatus";
import { useDependentMajors } from "@/hooks/useDependentMajors";
import { getSortedLevelOptions } from "@/utils/enums";
import {
    MoreOutlined,
    PlusOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    EditOutlined,
    DeleteOutlined,
} from "@ant-design/icons-vue";
import { useCustomDialog } from "@/components/CustomDialog/useCustomDialog";

const { templateList, fetchTemplateList } = useCategoryTemplateList();

const { activeDependentMajors } = useDependentMajors();

const customDialog = useCustomDialog();

// 左侧滚动区域引用
const leftScrollbar = ref(null);

// 是否正在加载
const loading = ref(false);

// 搜索大类关键词
const searchValue = ref("");

// 添加动态自定义下拉选项数据结构
const {
    options: dynamicOptions,
    updateOptions,
    updateOptionsFromOrgList,
} = useDynamicDropdownOptions(RelyingMajorOptions, RelyingMajorOptionsMap);

// 当前左侧选中的index
const currentIndex = ref(-1);

// 是否开启编辑状态
const isEdit = ref(false);

// 查看已关联绩效规则弹窗
const performanceRulesDialogVisible = ref(false);

// 已关联绩效规则列表
const performanceRulesList = ref<CmsTag_[]>([]);

// 切换编辑状态
function switchEdit(val: boolean) {
    isEdit.value = val;
    if (val === false) {
        saveEditTemplate(true);
    }
}

// 保存大类数据项排序
function saveEditTemplate(showMessage: boolean = false) {
    if (fieldsList.value.length === 0) {
        return Promise.resolve(); // 返回一个已解决的Promise对象
    }
    for (let i = 0; i < fieldsList.value.length; i++) {
        const element = fieldsList.value[i];
        element.sortOrder = i + 1;
    }

    // 仅保存id与sortOrder
    const temp = fieldsList.value.map((item) => {
        return {
            id: item.id,
            sortOrder: item.sortOrder,
        };
    });

    return cmsCategoryTemplateValueUpdateList({ body: temp }).then((res: any) => {
        if (res.code === 200) {
            if (showMessage) {
                customDialog.success({
                    title: "保存成功",
                    message: "如该表涉及绩效，请到绩效设置页面进行配置。",
                    showCancelButton: false,
                    alignCenter: true,
                });
            }
            loadDeatil(addValueForm.categoryTemplateId);
            isEdit.value = false;
        } else {
            customDialog.error({
                title: "保存失败",
                message: "当前保存未成功，请重试。",
                showCancelButton: false,
                alignCenter: true,
            });
        }
    });
}

// 数据表类型下拉框选项
const categoryTemplateTypeOptions = enumToOptions(categoryTemplateType);

// 审核负责人下拉框选项
const checkPersonOptions = ref<UmsPerson_[]>([]);

// 新增数据项弹窗
const addValueFormVisible = ref(false);

//  新增大类弹窗、新增大类表单ref、新增大类表单数据、校验规则
const addDialogFormVisible = ref(false);
const addTemplateFormRef = ref();
const addTemplateForm = reactive({
    id: null,
    templateName: "",
    templateType: null,
    classify: null,
    batchType: null,
    checkId: null,
    isSecretary: 0,
    description: "",
    enabled: null,
});

const addTemplateRules = reactive({
    templateName: [
        { required: true, message: "请输入数据表名称", trigger: "blur" },
        { max: 50, message: "数据表名称长度不能超过50个字符", trigger: "blur" },
    ],
    checkId: [{ required: true, message: "请选择审核负责人", trigger: "change" }],
    templateType: [{ required: true, message: "请选择数据表类型", trigger: "change" }],
    classify: [{ required: true, message: "请选择数据表分组", trigger: "change" }],
    isSecretary: [{ required: true, message: "请选择是否仅秘书录入", trigger: "change" }],
    batchType: [{ required: true, message: "请选择批次类型", trigger: "change" }],
});

// 新增/修改数据表表单提交
const submitAddTemplateForm = async (formEl: any) => {
    if (!formEl) return;
    try {
        await formEl.validate();
        if (addTemplateForm.id) {
            cmsCategoryTemplateUpdate({ body: addTemplateForm }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success({ message: `修改成功` });
                    addDialogFormVisible.value = false;
                    fetchTemplateList().then(() => {
                        filteredTemplateList.value = [...templateList.value]; // 初始化为完整列表
                    });
                } else {
                    ElMessage.error({ message: res.message });
                }
            });
        } else {
            cmsCategoryTemplateCreate({ body: addTemplateForm }).then((res: any) => {
                // TODO dataItemConfigurationStatus 数据项意味着需要完整配置项目负责人和项目名称，否则该表不予展示
                if (res.code === 200) {
                    ElMessage.success({ message: `新增成功` });
                    addDialogFormVisible.value = false;
                    // 新增成功后自动展示新表的详情
                    fetchTemplateList().then(() => {
                        if (templateList.value.length > 0) {
                            const lastIndex = templateList.value.length - 1;
                            const newTemplate = templateList.value[lastIndex];
                            // 更新筛选后的列表
                            filteredTemplateList.value = [...templateList.value];
                            loadDeatil(newTemplate.id, lastIndex, true);
                            // 滚动到底部
                            nextTick(() => {
                                if (leftScrollbar.value) {
                                    leftScrollbar.value.setScrollTop(9999); // 设置一个足够大的值来滚动到底部
                                }
                            });
                        }
                    });
                } else {
                    ElMessage.error({ message: res.message });
                }
            });
        }
    } catch (error) {
        console.log("表单验证失败:", error);
    }
};

// 新增弹窗关闭则清空数据项
watch(addDialogFormVisible, (newValue) => {
    if (newValue === false) {
        addTemplateFormRef.value?.resetFields();
        addTemplateForm.id = null;
        addTemplateForm.enabled = null;
    }
});

watch(addValueFormVisible, (newValue) => {
    if (newValue === false) {
        addValueForm.categoryPublicType = null;
        addValueForm.isRequired = 0;
        addValueForm.searchable = 0;
        addValueForm.sortOrder = 0;
        addValueForm.tag = "";
        addValueForm.description = "";
        addValueForm.type = null;
        addValueForm.value = "";
        addValueForm.id = null;
        addValueForm.parentId = 0;
        // 清空自定义选项为默认值
        customizeOptions.value = [];
    }
});

// 大类详情
const templateDetail = ref<CmsCategoryTemplate0>({} as CmsCategoryTemplate0);

// 大类数据项列表
const fieldsList = ref<CmsCategoryTemplateValue10[]>([]);

// 大类分类列表
const classifyList = ref<CmsClassify_[]>([]);

// 滚动容器引用
const scrollContainer = ref<HTMLElement | null>(null);

// 添加过滤后的列表引用
const filteredTemplateList = ref<CmsCategoryTemplate0[]>([]); // 新增过滤后的列表

// 加载大类详情
function loadDeatil(templateId: number, index: number = currentIndex.value, isRefresh: boolean = false) {
    // 修改判断条件，即使当前索引相同也允许加载，因为可能是搜索结果中的唯一项
    if (currentIndex.value === index && isRefresh && searchValue.value === "") {
        return;
    }
    currentIndex.value = index;
    isEdit.value = false;
    loading.value = true;
    // 此处重置下拉选项
    filterCategoryPublicTypeOptions.value = enumToOptionsGeneric(publicPerformanceType, publicPerformanceTypeMap);
    categoryPublicTypeOptions.value = enumToOptionsGeneric(publicPerformanceType, publicPerformanceTypeMap);

    // 用promise.all封装
    Promise.all([
        cmsCategoryTemplateGetById({ params: { id: templateId } }),
        cmsCategoryTemplateValueGetValueByTemplateId({
            params: { id: templateId },
        }),
    ]).then(([res1, res2]) => {
        templateDetail.value = res1.data;
        fieldsList.value = res2.data;
        loading.value = false;
        // 遍历filedsList，检测其中的categoryPublicType，去掉当前大类已经包含的公共标识符
        if (fieldsList.value != null) {
            fieldsList.value.forEach((field) => {
                // 参与人可重复出现
                if (field.categoryPublicType && field.categoryPublicType != publicPerformanceType.PROJECT_PARTICIPATE) {
                    const categoryPublicTypeValue = field.categoryPublicType;
                    filterCategoryPublicTypeOptions.value = filterCategoryPublicTypeOptions.value.filter(
                        (option) => categoryPublicTypeValue != option.value,
                    );
                }
            });
        }
    });
}

// 显示绩效规则弹窗
function showPerformanceRules(templateId: number) {
    performanceRulesDialogVisible.value = true;
    cmsTagCategoryEntityGetTagTreeListByEntityId({
        params: { categoryEntityId: templateId },
    }).then((res) => {
        performanceRulesList.value = res.data;
    });
}

// 新增数据项弹窗ref
const addValueFormRef = ref();

// 可作为哪些属性搜索下拉框选项
const categoryPublicTypeOptions = ref(enumToOptionsGeneric(publicPerformanceType, publicPerformanceTypeMap));

// 可作为哪些属性搜索下拉框选项(动态，筛选掉当前大类已包含的公共标识符)
const filterCategoryPublicTypeOptions = ref(enumToOptionsGeneric(publicPerformanceType, publicPerformanceTypeMap));

const addValueForm = reactive<CmsCategoryTemplateValue10>({
    categoryPublicType: null,
    categoryTemplateId: -1,
    isRequired: 0,
    searchable: 0,
    sortOrder: 0,
    description: "",
    tag: "",
    type: null,
    value: "",
    id: null,
    parentId: 0,
    dictList: [],
});

const addValueRules = reactive({
    value: [{ required: true, message: "请输入数据项名称", trigger: "blur" }],
    type: [{ required: true, message: "请选择数据项类型", trigger: "change" }],
    isRequired: [{ required: true, message: "请选择是否必填", trigger: "change" }],
});

// 新增、编辑数据项表单提交
const submitAddValueForm = async (formEl: any) => {
    // 当类型为枚举时，需要校验自定义选项
    if (
        addValueForm.type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
        addValueForm.type === categoryTemplateValueType_NEW.ENUM_MULTI
    ) {
        if (customizeOptions.value.length === 0) {
            ElMessage.error({ message: "请至少添加一个选项" });
            return;
        }
        if (customizeOptions.value.some((option) => option.label === "")) {
            ElMessage.error({
                message:
                    "第" +
                    (customizeOptions.value.findIndex((option) => option.label === "") + 1) +
                    "个选项名称为空！请输入！",
            });
            return;
        }
    }

    // 当类型为级别、依托专业时，需要校验动态选项
    if (
        addValueForm.type === categoryTemplateValueType_NEW.LEVEL ||
        addValueForm.type === categoryTemplateValueType_NEW.DEPENDENT_MAJOR
    ) {
        if (dynamicOptions.value.every((option) => option.enabled === 0)) {
            ElMessage.error({
                message: "请至少开启一个选项！",
            });
            return;
        }
    }
    if (!formEl) return;
    try {
        await formEl.validate();
        if (addValueForm.id === null) {
            if (!fieldsList.value || fieldsList.value.length === 0) {
                addValueForm.sortOrder = 1;
            } else {
                addValueForm.sortOrder = fieldsList.value.length + 1;
            }
            cmsCategoryTemplateValueCreate({ body: addValueForm as any }).then((res: any) => {
                if (res.code === 200) {
                    loadDeatil(addValueForm.categoryTemplateId);
                    ElMessage.success({ message: `新增成功` });
                    saveEditTemplate(false).then(() => {
                        addValueFormVisible.value = false;
                        isEdit.value = true;
                    });
                } else {
                    ElMessage.error({ message: res.message });
                }
            });
        } else {
            cmsCategoryTemplateValueUpdate({ body: addValueForm as any }).then((res: any) => {
                if (res.code === 200) {
                    ElMessage.success({ message: `修改成功` });
                    saveEditTemplate(false).then(() => {
                        addValueFormVisible.value = false;
                        loadDeatil(addValueForm.categoryTemplateId);
                        isEdit.value = true;
                    });
                } else {
                    ElMessage.error({ message: res.message });
                }
            });
        }
    } catch (error) {
        console.log("表单验证失败:", error);
    }
};

// 编辑数据项函数
function handleEditValue(item: CmsCategoryTemplateValue_) {
    cmsCategoryTemplateValueGetById({ params: { id: item.id } }).then((res) => {
        const data = res.data;
        addValueFormVisible.value = true;

        // 更新筛选后的选项
        filterCategoryPublicTypeOptions.value = enumToOptionsGeneric(publicPerformanceType, publicPerformanceTypeMap);
        if (fieldsList.value != null) {
            fieldsList.value.forEach((field) => {
                // 跳过当前正在编辑的数据项
                if (field.id === item.id) return;

                // 参与人可重复出现
                if (field.categoryPublicType && field.categoryPublicType != publicPerformanceType.PROJECT_PARTICIPATE) {
                    const categoryPublicTypeValue = field.categoryPublicType;
                    filterCategoryPublicTypeOptions.value = filterCategoryPublicTypeOptions.value.filter(
                        (option) => categoryPublicTypeValue != option.value,
                    );
                }
            });
        }

        addValueForm.categoryPublicType = data.categoryPublicType;
        addValueForm.dictList = data.dictList;
        addValueForm.isRequired = data.isRequired;
        addValueForm.searchable = data.searchable;
        addValueForm.sortOrder = data.sortOrder;
        addValueForm.tag = data.tag;
        addValueForm.description = data.description;
        addValueForm.type = data.type;
        addValueForm.value = data.value;
        addValueForm.id = data.id;
        addValueForm.parentId = data.parentId;

        const type = data.type;

        // 检查特殊字段类型，显示类型描述面板
        const specialTypes = [
            categoryTemplateValueType_NEW.PROJECT_NAME,
            categoryTemplateValueType_NEW.PROJECT_MAIN,
            categoryTemplateValueType_NEW.PROJECT_DATE,
        ];

        if (specialTypes.includes(type)) {
            // 找到对应的类型描述
            const typeDesc = findTypeDescription(type);
            if (typeDesc.html) {
                currentTypeDescriptionHtml.value = typeDesc.html;
                currentTypeTitle.value = typeDesc.title;
                nowDialogType.value = secondDialogType.TYPE_DESCRIPTION;
            }
        } else {
            currentTypeDescriptionHtml.value = null;
            currentTypeTitle.value = null;
            nowDialogType.value = null;
        }

        // 如果类型为字典等，则自动打开自定义选项弹窗
        if (
            type === categoryTemplateValueType_NEW.ENUM_SINGLE ||
            type === categoryTemplateValueType_NEW.ENUM_MULTI ||
            type === categoryTemplateValueType_NEW.PROJECT_STATUS
        ) {
            nowDialogType.value = secondDialogType.CUSTOMIZE;
            // 将字典列表转换为自定义选项
            customizeOptions.value = data.dictList.map((item) => ({
                label: item.name,
                value: item.id,
                isEdit: false,
                id: item.id,
                type: item.type,
                useCount: item.useCount,
            }));
        } else if (type === categoryTemplateValueType_NEW.LEVEL) {
            // 级别情况
            nowDialogType.value = secondDialogType.SWITCH;
            updateOptions(gradeOptions, gradeOptionsMap, true);

            // 首先将所有选项的enabled重置为0
            dynamicOptions.value.forEach((option) => {
                option.enabled = 0;
            });

            // 然后对data.dictList中存在的选项设置enabled为1,useCount为dictItem.useCount
            data.dictList.forEach((dictItem) => {
                const matchedOption = dynamicOptions.value.find((option) => option.label === dictItem.name);
                if (matchedOption) {
                    matchedOption.enabled = 1;
                    matchedOption.useCount = dictItem.useCount;
                }
            });
        }
    });
}

// 删除数据项函数
function handleDeleteValue(item: CmsCategoryTemplateValue_) {
    if (item.categoryPublicIsNoChange === 1) {
        ElMessage.error({ message: "该数据项为必填数据项，无法删除！" });
        return;
    }
    // 弹窗提示用户是否确认删除
    ElMessageBox.confirm("此操作将永久删除该数据项, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        // 用户确认删除，调用删除接口
        cmsCategoryTemplateValueDelete({ params: { id: item.id } }).then((res: any) => {
            if (res.code === 200) {
                ElMessage.success({ message: `删除成功` });
                saveEditTemplate(false).then(() => {
                    loadDeatil(addValueForm.categoryTemplateId);
                    isEdit.value = true;
                });
            } else {
                ElMessage.error({ message: res.message });
            }
        });
    });
}

// 二级弹窗类型
enum secondDialogType {
    /** 仅预览模式 */
    OVER_VIEW = 0,
    /** 开关模式 */
    SWITCH = 1,
    /** 自定义模式 */
    CUSTOMIZE = 2,
    /** 类型描述模式 */
    TYPE_DESCRIPTION = 3,
}
// 当前二级弹窗类型
const nowDialogType = ref<secondDialogType>();

// 自定义选项
const customizeOptions = ref<
    Array<{
        label: string;
        value: number | string;
        isEdit: boolean;
        id?: number;
        useCount?: number | null;
    }>
>([]);
// 声明 inputRefs 变量
const inputRefs = ref({});

// 当前显示的类型描述HTML内容
const currentTypeDescriptionHtml = ref<string | null>(null);
const currentTypeTitle = ref<string | null>(null);

// 从级联选择器选项中查找指定类型的描述
function findTypeDescription(type: number) {
    let result = { html: null, title: null };

    // 递归查找函数
    function findInOptions(options: any[]) {
        for (const option of options) {
            if (option.value === type) {
                return {
                    html: option.desc || null,
                    title: option.label || null,
                };
            }
            if (option.children && option.children.length) {
                const found = findInOptions(option.children);
                if (found.html) return found;
            }
        }
        return { html: null, title: null };
    }

    result = findInOptions(categoryTemplateValueTypeOptions_Cascader_NEW);
    return result;
}

// 设置 ref 的方法
const setInputRef = (index) => (el) => {
    if (el) {
        inputRefs.value[index] = el;
    } else {
        delete inputRefs.value[index]; // 组件销毁时删除对应的 ref
    }
};

// 新增自定义选项
function addCustomizeOption() {
    const newOption = {
        label: "",
        value: customizeOptions.value.length + 1,
        isEdit: false,
    };
    customizeOptions.value.push(newOption);
}

// 检查选项名称是否重复
function checkDuplicateOption(index) {
    const currentOption = customizeOptions.value[index];

    const duplicate = customizeOptions.value.findIndex(
        (opt, idx) => idx !== index && opt.label === currentOption.label && opt.label !== "",
    );

    if (duplicate !== -1) {
        ElMessage.error("添加失败，选项名称已存在！");
        customizeOptions.value.splice(index, 1);
        return true;
    }

    return false;
}

// 编辑自定义选项
const editOption = async (index) => {
    customizeOptions.value[index].isEdit = true; // 设置为编辑状态
    await nextTick(); // 等待 DOM 更新
    const inputRef = inputRefs.value[index]; // 获取对应的输入框引用
    if (inputRef) {
        inputRef.input.focus(); // 调用 Element Plus 输入框的 focus 方法
    }
};

// 删除自定义选项
function deleteCustomizeOption(index: number) {
    customizeOptions.value.splice(index, 1);
}

// 级联选择器监听事件
function handleCategoryTemplateValueTypeChange(val: any) {
    console.log("级联：", val);
    addValueForm.type = val;
    customizeOptions.value = [];
    // 级别情况
    if (val === categoryTemplateValueType_NEW.LEVEL) {
        // 切换到开关型菜单
        nowDialogType.value = secondDialogType.SWITCH;
        addValueForm.searchable = 1;
        addValueForm.categoryPublicType = publicPerformanceType.GRADE;
        updateOptions(gradeOptions, gradeOptionsMap, true);
    } else if (val === categoryTemplateValueType_NEW.PROJECT_STATUS) {
        // 切换到自定义型菜单
        nowDialogType.value = secondDialogType.CUSTOMIZE;
        addValueForm.categoryPublicType = null;
        addValueForm.searchable = 0;
        customizeOptions.value = [
            {
                label: ProjectStatusMap[ProjectStatus.APPROVAL],
                value: ProjectStatus.APPROVAL,
                isEdit: false,
            },
            {
                label: ProjectStatusMap[ProjectStatus.DECLARE],
                value: ProjectStatus.DECLARE,
                isEdit: false,
            },
            {
                label: ProjectStatusMap[ProjectStatus.END_ITEM],
                value: ProjectStatus.END_ITEM,
                isEdit: false,
            },
        ];
    } else if (val === categoryTemplateValueType_NEW.DEPENDENT_MAJOR) {
        nowDialogType.value = null;
        // 切换到开关型菜单
        nowDialogType.value = secondDialogType.OVER_VIEW;
        // 使用新函数从orgList生成选项
        updateOptionsFromOrgList(activeDependentMajors.value);
        addValueForm.searchable = 0;
        addValueForm.categoryPublicType = null;
    } else if (val === categoryTemplateValueType_NEW.ENUM_MULTI || val === categoryTemplateValueType_NEW.ENUM_SINGLE) {
        // 切换到自定义型菜单
        nowDialogType.value = secondDialogType.CUSTOMIZE;
        addValueForm.categoryPublicType = null;
        addValueForm.searchable = 0;
    } else if (val == 0 || val) {
        addValueForm.categoryPublicType = null;
        addValueForm.searchable = 0;
        nowDialogType.value = undefined;
    }
}

// 编辑大类函数
function editTemplate(row: CmsCategoryTemplate0) {
    addDialogFormVisible.value = true;
    // 调用nextTick，等到弹窗出现后再赋值
    nextTick(() => {
        Object.assign(addTemplateForm, row);
    });
}

// 删除大类
function deleteTemplate(id: number) {
    // 弹窗确认是否删除
    ElMessageBox.confirm("此操作将永久删除该数据表, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        cmsCategoryTemplateDelete({ params: { id } }).then((res: any) => {
            if (res.code === 200) {
                ElMessage.success({ message: `删除成功` });
                templateDetail.value = {} as CmsCategoryTemplate0;
                fetchTemplateList().then(() => {
                    filteredTemplateList.value = [...templateList.value]; // 初始化为完整列表
                });
            } else {
                ElMessage.error({ message: res.message });
            }
        });
    });
}

// 监听addValueForm上的searchable，如果为0，则清空categoryPublicType
watch(
    addValueForm,
    (newValue) => {
        if (newValue.searchable === 0) {
            addValueForm.categoryPublicType = null;
        }
    },
    { deep: true },
);

// 监听templateDetail,发生变化后，将其身上id赋给addValueForm.categoryTemplateId
// 同时负责禁用级别下拉选择
// TODO 后续依托专业也需要锁定
watch(
    templateDetail,
    (newValue) => {
        addValueForm.categoryTemplateId = newValue?.id;

        // 使用深拷贝避免修改原始对象
        categoryTemplateValueTypeOptions_Cascader_Ref.value = JSON.parse(
            JSON.stringify(categoryTemplateValueTypeOptions_Cascader_NEW),
        );

        // 在这里添加我们的新禁用逻辑
        // 为了处理嵌套结构，我们需要一个递归函数来遍历并禁用特定选项
        const disableValueTypes = (options, disabledTypes) => {
            if (!options) return;

            options.forEach((option) => {
                if (option.children) {
                    // 递归处理子选项
                    disableValueTypes(option.children, disabledTypes);
                } else if (disabledTypes.includes(option.value)) {
                    // 禁用指定类型的选项
                    option.disabled = true;
                }
            });
        };

        // 找出已存在的公共标识符类型
        const existingTypes = [];
        if (fieldsList.value) {
            fieldsList.value.forEach((field) => {
                // 不允许重复出现的公共标识符，禁用对应类型
                if (
                    field.type === categoryTemplateValueType_NEW.PROJECT_NAME ||
                    field.type === categoryTemplateValueType_NEW.PROJECT_STATUS ||
                    field.type === categoryTemplateValueType_NEW.PROJECT_MAIN ||
                    field.type === categoryTemplateValueType_NEW.PROJECT_DATE
                ) {
                    existingTypes.push(field.type);
                }
            });
        }

        // 同时禁用特定类型的选项
        // 如果当前模板类型为院情数据或工作数据，也禁用LEVEL
        // 暂时取消禁用院情数据和工作数据下的级别
        const flag =
            fieldsList.value && fieldsList.value.some((item) => item.type === categoryTemplateValueType_NEW.LEVEL);
        if (
            // newValue?.templateType === categoryTemplateType["院情数据"] ||
            // newValue?.templateType === categoryTemplateType["工作数据"] ||
            flag
        ) {
            existingTypes.push(categoryTemplateValueType_NEW.LEVEL);
        }
        // 应用禁用逻辑
        disableValueTypes(categoryTemplateValueTypeOptions_Cascader_Ref.value, existingTypes);
    },
    { deep: true },
);

// 监听customizeOptions，发生变化时，将其值赋给addValueForm.dictList
watch(
    customizeOptions,
    (newValue) => {
        addValueForm.dictList = newValue.map((item) => ({
            name: item.label,
            id: item.id,
        }));
    },
    {
        deep: true,
        immediate: true,
    },
);

/**
 * 锁定数据项
 * @param isTypeField 是否为特殊数据项（数据项类型）
 */
const isLockField = (isTypeField: boolean = false) => {
    // 如果是新增模式，则不锁定
    if (addValueForm.id === null) {
        return false;
    }
    // 如果是类型数据项且模板已启用，则禁用
    if (isTypeField && templateDetail.value?.enabled === 1) {
        return true;
    }

    // 如果是院情数据，则不锁定
    if (templateDetail.value.templateType === categoryTemplateType["院情数据"]) {
        return false;
    }
    // 如果是修改模式且数据项为特殊数据项，则全部禁用
    if (
        addValueForm.id &&
        (addValueForm.categoryPublicType === publicPerformanceType.PROJECT_NAME ||
            addValueForm.categoryPublicType === publicPerformanceType.PROJECT_MAIN ||
            addValueForm.categoryPublicType === publicPerformanceType.DATE)
    ) {
        return true;
    }

    return false;
};

const categoryTemplateValueTypeOptions_Cascader_Ref = ref(null);

// 监听dynamicOptions，发生变化时，将其值赋给addValueForm.dictList
watch(
    dynamicOptions,
    (newValue) => {
        addValueForm.dictList = newValue
            .filter((item: any) => item.enabled === 1)
            .map((item: any) => ({
                name: item.label,
                id: item.id,
            }));
    },
    { deep: true },
);

// 获取大类分类列表
onMounted(() => {
    // 根据权限卡id获取有 审核权限与管理员录入权限 的人员列表
    const cards = [PermissionIds[PermissionEnum.CHECK_PERMISSION], PermissionIds[PermissionEnum.ADMIN_ENTRY]];
    umsPersonAuditList({ params: { cards } }).then((res) => {
        checkPersonOptions.value = res.data;
    });
    cmsClassifyList({}).then((res) => {
        classifyList.value = res.data;
    });
    fetchTemplateList().then(() => {
        filteredTemplateList.value = [...templateList.value]; // 初始化为完整列表
        if (templateList.value.length > 0) {
            const newTemplate = templateList.value[0];
            loadDeatil(newTemplate.id, 0, true);
        }
    });
});

// 添加搜索值的watch监听
watch(searchValue, (newVal) => {
    if (!newVal) {
        filteredTemplateList.value = [...templateList.value];
    } else {
        filteredTemplateList.value = templateList.value.filter((item) => item.templateName.includes(newVal));
    }
});

// 新增编辑选项完成逻辑
function finishEditing(index) {
    if (checkDuplicateOption(index)) {
        return;
    }
    customizeOptions.value[index].isEdit = false;
}

// 防止动画期间重复点击
const isAnimating = ref(false);

// 向上移动项目
function moveItemUp(item: CmsCategoryTemplateValue_) {
    if (isAnimating.value) return; // 防止重复点击

    const currentIndex = fieldsList.value.findIndex((field) => field.id === item.id);
    if (currentIndex > 0) {
        isAnimating.value = true;
        // 添加动画类名
        const currentEl = document.querySelector(`[data-item-id="${item.id}"]`) as HTMLElement;
        const targetEl = document.querySelector(
            `[data-item-id="${fieldsList.value[currentIndex - 1].id}"]`,
        ) as HTMLElement;

        if (currentEl && targetEl) {
            // 添加移动类名和动画
            currentEl.classList.add("moving");
            targetEl.classList.add("moving");
            currentEl.style.transition = "transform 0.3s cubic-bezier(0.2, 0, 0.2, 1)";
            targetEl.style.transition = "transform 0.3s cubic-bezier(0.2, 0, 0.2, 1)";

            // 获取元素高度
            const currentHeight = currentEl.offsetHeight;
            const targetHeight = targetEl.offsetHeight;

            // 应用变换
            currentEl.style.transform = `translateY(-${targetHeight + 17}px)`; // 17px是margin-bottom
            targetEl.style.transform = `translateY(${currentHeight + 17}px)`;

            // 动画结束后更新数据
            setTimeout(() => {
                // 交换当前项目和上一个项目的位置
                const temp = fieldsList.value[currentIndex];
                fieldsList.value[currentIndex] = fieldsList.value[currentIndex - 1];
                fieldsList.value[currentIndex - 1] = temp;

                // 清除样式和类名
                currentEl.classList.remove("moving");
                targetEl.classList.remove("moving");
                currentEl.style.transition = "";
                currentEl.style.transform = "";
                targetEl.style.transition = "";
                targetEl.style.transform = "";

                // 重置动画标志
                isAnimating.value = false;
            }, 300);
        } else {
            // 如果没有找到元素，直接交换
            const temp = fieldsList.value[currentIndex];
            fieldsList.value[currentIndex] = fieldsList.value[currentIndex - 1];
            fieldsList.value[currentIndex - 1] = temp;

            isAnimating.value = false;
        }
    }
}

// 向下移动项目
function moveItemDown(item: CmsCategoryTemplateValue_) {
    if (isAnimating.value) return; // 防止重复点击

    const currentIndex = fieldsList.value.findIndex((field) => field.id === item.id);
    if (currentIndex < fieldsList.value.length - 1) {
        isAnimating.value = true;
        // 添加动画类名
        const currentEl = document.querySelector(`[data-item-id="${item.id}"]`) as HTMLElement;
        const targetEl = document.querySelector(
            `[data-item-id="${fieldsList.value[currentIndex + 1].id}"]`,
        ) as HTMLElement;

        if (currentEl && targetEl) {
            // 添加移动类名和动画
            currentEl.classList.add("moving");
            targetEl.classList.add("moving");
            currentEl.style.transition = "transform 0.3s cubic-bezier(0.2, 0, 0.2, 1)";
            targetEl.style.transition = "transform 0.3s cubic-bezier(0.2, 0, 0.2, 1)";

            // 获取元素高度
            const currentHeight = currentEl.offsetHeight;
            const targetHeight = targetEl.offsetHeight;

            // 应用变换
            currentEl.style.transform = `translateY(${targetHeight + 17}px)`; // 17px是margin-bottom
            targetEl.style.transform = `translateY(-${currentHeight + 17}px)`;

            // 动画结束后更新数据
            setTimeout(() => {
                // 交换当前项目和下一个项目的位置
                const temp = fieldsList.value[currentIndex];
                fieldsList.value[currentIndex] = fieldsList.value[currentIndex + 1];
                fieldsList.value[currentIndex + 1] = temp;

                // 清除样式和类名
                currentEl.classList.remove("moving");
                targetEl.classList.remove("moving");
                currentEl.style.transition = "";
                currentEl.style.transform = "";
                targetEl.style.transition = "";
                targetEl.style.transform = "";

                // 重置动画标志
                isAnimating.value = false;
            }, 300);
        } else {
            // 如果没有找到元素，直接交换
            const temp = fieldsList.value[currentIndex];
            fieldsList.value[currentIndex] = fieldsList.value[currentIndex + 1];
            fieldsList.value[currentIndex + 1] = temp;
            isAnimating.value = false;
        }
    }
}
</script>

<style scoped lang="scss">
@use "../../../../assets/styles/mixins.scss" as mix;
@use "../../../../assets/styles/modals.scss";
@use "../../../../assets/styles/templateFieldsDetail.scss";
:global(.table-management-cascader.is-pure) {
    z-index: 5000 !important;
}

// 覆盖el-popover样式,取消min-width限制
:global(.el-popover.edit-popover) {
    min-width: unset !important;
    padding: 5px 0px !important;
}

// 覆盖el-tooltip样式
:global(.el-popper.is-customized.cascader-tooltip) {
    /* Set padding to ensure the height is 32px */
    z-index: 5001 !important;
    padding: 0 !important;
    font-weight: 400;
    width: 260px; // 限制悬浮窗最大宽度
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
    border-radius: 10px;
}

:global(.el-popper.is-customized.cascader-tooltip .el-popper__arrow::before) {
    padding: 0;
    background: transparent;
    right: 0;
}

$padding: 0 30px;

// 每行最低高度
$min-height: 70px;

.handle {
    cursor: grab;
}

// 院情
.type-1 {
    background: #fff7e6;
    border: 1px solid #ffd591;
    color: #d46b08;
}
// 工作
.type-2 {
    border: 1px solid #87e8de;
    background-color: #e6fffb;
    color: #08979c;
}
// 成果
.type-3 {
    background: #e6f4ff;
    border: 1px solid #91caff;
    color: #0958d9;
}

.my-dialog {
    z-index: 4000;
    position: absolute;
    border-radius: 10px;
    padding: 0px 15px 15px 15px;
    width: 380px;
    height: 480px;
    background-color: rgb(255, 255, 255);
    border: 1px solid #d3d3d3;
    overflow: auto;
    top: 15%;
    left: 50% !important; // 先将弹窗定位到页面中间
    transform: translateX(calc(50% + 60px)) !important; // 向右偏移旧弹窗宽度的一半加上间距
}

.active {
    background-color: #f4e4cc;
}

.dropdown-settings-modal {
    background-color: #fff !important;
}

.fade-enter-active,
.fade-leave-active {
    transition:
        opacity 0.4s ease-in-out,
        top 0.4s ease;
}

.fade-enter-from,
.fade-leave-to {
    top: 10%;
    opacity: 0;
}

// 动画和拖拽样式已移动到 templateFieldsDetail.scss

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wrapper {
    height: 100%;
    overflow: hidden;

    .table {
        height: 100%;
        display: flex;
        background: #cedaf1;
        border-radius: 6px 6px 0 0;
        .left {
            height: 100%;
            width: 188px;
            display: flex;
            flex-direction: column;
            color: #23346d;

            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.3; /* 17px */
            letter-spacing: 1.3px;

            .left-scroll {
                flex: 1;
                height: 0;
                display: flex;
                flex-direction: column;
                .search-box {
                    padding: 16px 8px 8px 8px;
                    border-radius: 6px 0px 0px 0px;
                }
                .left-title {
                    display: flex;
                    width: 188px;
                    padding: 8px 16px;
                    align-items: center;
                    flex-shrink: 0;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
                    color: #a0add9;
                    font-size: 13px;
                    font-weight: 400;
                }
                .left-list {
                    flex: 1;
                    height: 0;
                }
                .template {
                    display: flex;
                    width: 188px;
                    padding: 8px 8px 8px 16px;
                    align-items: center;
                    flex-shrink: 0;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
                    cursor: pointer;
                    transition: all 0.2s;

                    .template-name {
                        width: 108px;
                        height: 100%;
                        text-wrap: nowrap;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 1;
                        line-clamp: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &:hover {
                        background-color: #f4e4cc;
                    }
                    .type {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;
                        border-radius: 4px;
                        width: 38px;
                        height: 22px;
                        padding: 0 7px;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 20px;
                    }
                }
            }

            .left-bottom {
                border-top: 1px solid #f4f4f4;
                padding: 35px;
                display: flex;
                align-items: center;
                justify-content: center;
                // height: calc(100vh - 650px);
            }
        }

        .right {
            height: 100%;
            position: relative;
            /* @include mix.custom-scrollBar-height; */
            background: #fff;
            width: 100%;
            // overflow-y: auto;
            scroll-behavior: smooth;
            .right-scroll {
                height: 100%;
                display: flex;
                flex-direction: column;

                .right-content {
                    flex: 1;
                    height: 0;
                }
            }
        }
    }
}

.dropdown-settings {
    .settings-title {
        font-size: 18px;
        font-weight: 500;
        margin-top: 15px;
        margin-bottom: 5px;
        letter-spacing: 0.5px;
        color: #23346d;
    }

    .settings-subTitle {
        color: #818181;
        font-size: 14px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eff3f9;
    }

    .settings-content {
        .option-item {
            margin-bottom: 10px;
            background-color: #eff3f9;
            padding: 0 10px;
            height: 40px;
            line-height: 40px;
            border-radius: 5px;

            .option-label {
                color: #606266;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .option-value {
                    width: 220px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

// 修改自定义弹窗样式
:deep(.level-dialog) {
    top: 15%;
    left: 51% !important; // 先将弹窗定位到页面中间
    transform: translateX(calc(50% + 20px)) !important; // 向右偏移旧弹窗宽度的一半加上间距
    margin: 0 !important;
    pointer-events: auto !important;
}

/* 类型描述面板样式 */
.type-description-dialog {
    height: auto;
    padding: 10px 15px 15px 15px;
}

.description-text {
    line-height: 1.6;
    color: #333;
    font-size: 14px;
    padding: 10px;
}

.type-description-title {
    font-size: 18px;
    border-bottom: 1px solid #8795b2;
    padding-bottom: 10px;
}

// 重写预览弹窗的样式
:deep(.cascader-title) {
    background-color: unset;
    width: 100%;
    padding: 16px 24px 0px 24px;
}
</style>
