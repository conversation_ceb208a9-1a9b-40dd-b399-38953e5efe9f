<template>
    <div class="performance-score">
        <div class="header">
            <div class="header-title">
                {{
                    personId ? getPersonName(allPersonListWithDisabled, personId.toString()) + "的绩效得分" : "我的绩效"
                }}
            </div>
        </div>
        <div class="content" v-loading="loading">
            <div class="content-data-picker">
                <div>
                    <el-button v-if="personId" @click="handleBack">返回</el-button>
                </div>
                <div>
                    选择时间：
                    <el-date-picker
                        format="YYYY年度"
                        :clearable="false"
                        @change="changeYear"
                        style="width: 150px"
                        v-model="nowYear"
                        type="year"
                        placeholder="Pick a year" />
                </div>
            </div>
            <div class="content-data">
                <div class="content-header" v-drag-scroll>
                    <div class="each-item" v-for="(item, index) in overViewData" :key="index">
                        <div class="each-item-content">
                            <div class="left-item">{{ item.name }}</div>
                            <div class="right-item">{{ item.score }}</div>
                        </div>
                    </div>
                </div>
                <div class="table">
                    <el-table
                        :header-cell-style="{ backgroundColor: '#D9E3F6', color: '#6B7995', fontWeight: '600' }"
                        :cell-style="{ border: 'none' }"
                        :indent="40"
                        cell-class-name="performance-cell"
                        :data="scoreTree"
                        default-expand-all
                        style="width: 100%"
                        height="100%"
                        row-key="id">
                        <el-table-column align="right" fixed type="" prop="maxScore" label="最终得分">
                            <template #default="scope">
                                <div
                                    style="display: flex; justify-content: end; width: 100%"
                                    v-if="scope.row.score === 0">
                                    <div style="color: #3b82f6; font-weight: 700">{{ scope.row.actualScore }}</div>
                                    <div>/{{ scope.row.maxScore }}</div>
                                </div>
                                <div style="display: flex; justify-content: end; width: 100%" v-else>
                                    <span style="color: #3b82f6; font-weight: 700"> {{ scope.row.score }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" prop="tagName" label="绩效规则" min-width="500">
                            <template #default="scope">
                                <div :class="scope.row.tagLevel === null ? 'rowData-cell' : 'flex-center'">
                                    <div>{{ scope.row.tagName }}</div>
                                    <span v-if="scope.row.tagRules">
                                        <el-tooltip placement="right" popper-class="tag-tooltip" effect="light">
                                            <template #content>
                                                <div>
                                                    <div
                                                        style="margin: 5px 0"
                                                        v-for="(item, i) in scope.row.tagRules"
                                                        :key="i">
                                                        {{ i + 1 }}：{{ item }}
                                                    </div>
                                                </div>
                                            </template>
                                            <div class="flex-center" v-if="scope.row.tagRules">
                                                <svg
                                                    width="20"
                                                    height="20"
                                                    viewBox="0 0 21 21"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <circle
                                                        cx="10.5"
                                                        cy="10.5"
                                                        r="10"
                                                        fill="#C9CDDC"
                                                        stroke="#B9BED1" />
                                                    <path
                                                        d="M9.23535 6.99902C9.23535 6.66178 9.35384 6.38835 9.59082 6.17871C9.83236 5.96452 10.14 5.85742 10.5137 5.85742C10.8965 5.85742 11.2064 5.9668 11.4434 6.18555C11.6803 6.39974 11.7988 6.6709 11.7988 6.99902C11.7988 7.33171 11.6781 7.6097 11.4365 7.83301C11.195 8.05176 10.8874 8.16113 10.5137 8.16113C10.1445 8.16113 9.83919 8.04948 9.59766 7.82617C9.35612 7.60286 9.23535 7.32715 9.23535 6.99902ZM9.39941 17V9.43945H11.6006V17H9.39941Z"
                                                        fill="white" />
                                                </svg>
                                            </div>
                                        </el-tooltip>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="countScore" label="分值" min-width="200">
                            <template #default="scope">
                                <div class="flex-center" style="gap: 10px">
                                    <OverflowableProgress
                                        v-if="scope.row.score === 0"
                                        :percentage="(scope.row.countScore / scope.row.maxScore) * 100"
                                        :maxValue="100"
                                        normalColor="#409eff"
                                        overflowColor="#f56c6c"
                                        :textInside="true"
                                        :stroke-width="15"
                                        width="200px">
                                        <template #default>
                                            <span>{{ scope.row.countScore }}/{{ scope.row.maxScore }}</span>
                                        </template>
                                    </OverflowableProgress>
                                    <!-- 如果溢出则提示 -->
                                    <span v-if="(scope.row.countScore / scope.row.maxScore) * 100 > 100">
                                        <el-tooltip placement="top" popper-class="alert-tooltip" effect="light">
                                            <template #content>
                                                <div>
                                                    您在该指标的绩效总分有溢出，原本得分为<span
                                                        style="color: #3b82f6; font-weight: 700"
                                                        >{{ scope.row.countScore }}</span
                                                    >分，该项指标的分数上限为<span
                                                        style="color: #3b82f6; font-weight: 700"
                                                        >{{ scope.row.maxScore }}</span
                                                    >分。 您的该项指标的绩效已经被记录为<span
                                                        style="color: #3b82f6; font-weight: 700"
                                                        >{{ scope.row.maxScore }}</span
                                                    >分。
                                                </div>
                                            </template>
                                            <div
                                                class="flex-center"
                                                v-if="(scope.row.countScore / scope.row.maxScore) * 100 > 100">
                                                <svg
                                                    width="20"
                                                    height="20"
                                                    viewBox="0 0 21 21"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <circle
                                                        cx="10.5"
                                                        cy="10.5"
                                                        r="10"
                                                        fill="#EECD88"
                                                        stroke="#B9BED1" />
                                                    <path
                                                        d="M9.23535 6.99902C9.23535 6.66178 9.35384 6.38835 9.59082 6.17871C9.83236 5.96452 10.14 5.85742 10.5137 5.85742C10.8965 5.85742 11.2064 5.9668 11.4434 6.18555C11.6803 6.39974 11.7988 6.6709 11.7988 6.99902C11.7988 7.33171 11.6781 7.6097 11.4365 7.83301C11.195 8.05176 10.8874 8.16113 10.5137 8.16113C10.1445 8.16113 9.83919 8.04948 9.59766 7.82617C9.35612 7.60286 9.23535 7.32715 9.23535 6.99902ZM9.39941 17V9.43945H11.6006V17H9.39941Z"
                                                        fill="white" />
                                                </svg>
                                            </div>
                                        </el-tooltip>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getPersonName } from "@/utils/getNames";
import {
    pmsScoreGetMyScoreTotal,
    pmsScoreGetScoreListByYearAndPersonId,
    pmsScoreGetScoreTotalByPersonId,
    pmsScoreGetScoreTreeByYearAndPersonId,
    pmsScoreGetScoreTreeByYear,
    pmsScoreMajorGetScoreTotalByPersonIdAndDept,
    pmsScoreMajorGetScoreTreeByYearAndPersonId,
} from "@/apis/pmsScoreController";
import { CmsTagNode_, PmsScoreTotalDto } from "@/apis/types";
import OverflowableProgress from "@/components/OverflowableProgress/index.vue";
import { usePersons } from "@/hooks/usePersons";

// 组件属性定义
const props = defineProps<{
    /**
     * 人员ID，用于查询指定人员的绩效得分
     * 如果不提供，则查询当前登录用户的绩效得分
     */
    personId?: number | string;
    /**
     * 默认年份
     */
    defaultYear?: Date;
    /**
     * 是否为专业绩效得分
     */
    isProfessional?: boolean;
    /**
     * 自定义返回逻辑
     */
    customBack?: () => void;
}>();

// 组件事件
const emit = defineEmits<{
    (e: "back"): void;
}>();

// 当前人员id，可能从props接收，也可能是undefined
const personId = ref<number | undefined>(props.personId ? Number(props.personId) : undefined);

// 获取所有人员列表(包含已被禁用的人员)
const { allPersonListWithDisabled } = usePersons(true);

// 获取当前年份
const nowYear = ref(props.defaultYear || new Date());

// 绩效得分概览
const overViewData = ref<PmsScoreTotalDto[]>([]);

// 绩效得分树
const scoreTree = ref<CmsTagNode_[]>([]);

// 加载状态
const loading = ref(false);

/**
 * 获取绩效数据
 */
const fetchData = async () => {
    loading.value = true;
    if (personId.value && !props.isProfessional) {
        // 有人员id，且 不是专业绩效得分，则查询人员信息和获取人员绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreGetScoreTotalByPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
            pmsScoreGetScoreTreeByYearAndPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    } else if (props.isProfessional && personId.value) {
        // 有人员id，且 为专业绩效得分，则查询人员信息和获取人员绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreMajorGetScoreTotalByPersonIdAndDept({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
            pmsScoreMajorGetScoreTreeByYearAndPersonId({
                params: { year: nowYear.value.getFullYear(), personId: personId.value },
            }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    } else {
        // 没有人员id，则查询我的绩效得分和获取绩效得分树
        const [res1, res2] = await Promise.all([
            pmsScoreGetMyScoreTotal({ params: { year: nowYear.value.getFullYear() } }),
            pmsScoreGetScoreTreeByYear({ params: { year: nowYear.value.getFullYear() } }),
        ]);
        overViewData.value = res1.data;
        scoreTree.value = res2.data;
        loading.value = false;
    }
};

/**
 * 年份变化事件处理
 */
const changeYear = (val: Date) => {
    nowYear.value = new Date(val);
    fetchData();
};

/**
 * 返回按钮点击事件处理
 */
const handleBack = () => {
    if (props.customBack) {
        props.customBack();
    } else {
        emit("back");
    }
};

// 组件挂载时获取数据
onMounted(() => {
    fetchData();
});
</script>

<style scoped lang="scss">
// 修改tooltip样式
:global(.tag-tooltip) {
    font-size: 16px;
    max-width: 600px;
    border-radius: 10px;
    color: #6b7995;
}

:global(.alert-tooltip) {
    border-radius: 10px;
    color: #6b7995;
    width: 300px;
}
// 更改表格第一列的cell为左对齐
:deep(.performance-cell div) {
    display: flex;
    align-items: center;
}
// 去掉表格最底部的线
:deep(.el-table__inner-wrapper:before) {
    height: 0;
}

:deep(td.performance-cell:has(.rowData-cell)) {
    padding: 0 !important;
}
:deep(td.performance-cell:has(.rowData-cell) .cell) {
    height: 39px;
}

.rowData-cell {
    color: #6b7995;
    width: 100%;
    height: 100%;
    background-color: #f9fafb;
    padding-left: 20px;
}

.flex-center {
    display: flex;
    align-items: center;
    gap: 10px;
}

.performance-score {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 24px 24px 0;
    gap: 24px;

    background: #ffffff;
    border-radius: 6px 6px 0px 0px;
    .header {
        height: 80px;

        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9fbff;

        .header-title {
            color: #23346d;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 34px; /* 1.7 */
            letter-spacing: 2.4px;
            text-align: center;
        }
    }
    .content {
        flex: 1;
        height: 0;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        gap: 10px;

        .content-data-picker {
            display: flex;
            flex-direction: column;
        }
        .content-data {
            flex: 1;
            height: 0;
            display: flex;
            padding: 24px 16px;
            gap: 16px;
            flex-direction: column;
            align-items: center;

            background: #f9fbff;

            .content-header {
                display: flex;
                gap: 8px;
                align-items: center;
                flex-shrink: 0;

                overflow-x: auto;
                white-space: nowrap;
                width: 100%;
                user-select: none;
                &::-webkit-scrollbar {
                    height: 6px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: #d9d9d9;
                    border-radius: 3px;
                    cursor: pointer;
                }
                &::-webkit-scrollbar-track {
                    background-color: #f5f5f5;
                }
                .each-item {
                    display: flex;
                    width: 300.8px;
                    height: 88px;
                    padding: 1px 24px;
                    gap: 10px;
                    flex-direction: column;
                    justify-content: center;
                    align-items: flex-start;
                    flex-shrink: 0;

                    background: #ffffff;
                    border: 1px solid #f0f0f0;
                    border-radius: 8px;

                    .each-item-content {
                        height: 54px;
                        display: flex;
                        gap: 4px;
                        flex-direction: column;
                        align-items: flex-start;
                        flex-shrink: 0;
                    }

                    // 第一个为总分，字体变大
                    &:nth-child(1) {
                        .left-item,
                        .right-item {
                            // font-size: 24px;
                        }
                    }
                    // 最后一个为减分指标
                    &:last-child {
                        .right-item {
                            color: #bb512fcc;
                        }
                    }
                    .left-item {
                        height: 22px;
                        color: rgba(0, 0, 0, 0.45);
                        font-family: "Roboto";
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 22px; /* 1.571 */
                    }
                    .right-item {
                        height: 28px;
                        color: #1677ff;
                        font-family: "Roboto";
                        font-size: 24px;
                        font-style: normal;
                        font-weight: 400;
                    }
                }
            }
            .table {
                flex: 1;
                height: 0;
                width: 100%;
                position: relative;
                :deep(.el-table) {
                    position: absolute;
                }
            }
        }
    }
}
</style>
