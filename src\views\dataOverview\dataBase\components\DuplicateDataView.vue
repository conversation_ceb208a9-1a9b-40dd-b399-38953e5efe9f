<template>
    <div class="repeat-data-container" v-loading="duplicateImportLoading">
        <!-- 表自身的重复数据展示 -->
        <div
            class="repeat-data-content"
            v-if="checkFailInfo?.errorList[0].repetitionFile?.length > 0">
            <div class="error-list">
                <div class="repeat-data-header">
                    <div>
                        <span style="color: #1677ff">存在重复的数据表：</span>
                        <span class="template-name">{{
                            checkFailInfo?.template.templateName || "未知文件"
                        }}</span>
                    </div>
                </div>
                <div
                    class="repeat-data-item"
                    v-for="(errorItem, index) in checkFailInfo?.errorList"
                    :key="index">
                    <!-- 表A区域 -->
                    <div class="error-header">
                        导入文件
                        <span style="color: #de7a5c">第{{ errorItem.repetitionRow[0].rowId }}行 </span>
                        数据与
                        <span style="color: #de7a5c" v-for="vrow in errorItem.repetitionFile">
                            第{{ vrow.rowId }}行 
                        </span>
                        数据发现重复
                    </div>
                    <el-table
                        header-row-class-name="table-header"
                        :row-class-name="'warning-row'"
                        :data="errorItem.repetitionRow"
                        border
                        style="width: 100%">
                        <el-table-column align="center" label="状态" min-width="100">
                            <template #default="">
                                <div>待导入</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-for="fieldName in checkFailInfo?.templateValueList"
                            :key="fieldName.id"
                            align="center"
                            :label="fieldName.value"
                            :prop="fieldName.value">
                            <template #default="scope">
                                <FieldValueDisplay
                                    :field="fieldName"
                                    :row-data="scope.row"
                                    :person-list="allPersonListWithDisabled"
                                    :dict-list="dictList"
                                    :org-list="allDependentMajors" />
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="操作" fixed="right">
                            <template #default="scope">
                                <el-button type="primary" @click="$emit('downloadMaterial', scope.row, true)">
                                    下载材料
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 表B区域 -->
                    <div class="error-header">
                        第<span style="color: #de7a5c" v-for="vrow in errorItem.repetitionFile">
                            {{ vrow.rowId }}行 
                        </span>
                        数据：
                    </div>
                    <el-table
                        :data="errorItem.repetitionFile"
                        border
                        :row-class-name="'warning-row'"
                        style="width: 100%">
                        <el-table-column align="center" label="状态" min-width="100">
                            <template #default="">
                                <div>待导入</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-for="fieldName in checkFailInfo?.templateValueList"
                            :key="fieldName.id"
                            align="center"
                            :label="fieldName.value"
                            :prop="fieldName.value">
                            <template #default="scope">
                                <FieldValueDisplay
                                    :field="fieldName"
                                    :row-data="scope.row"
                                    :person-list="allPersonListWithDisabled"
                                    :dict-list="dictList"
                                    :org-list="allDependentMajors" />
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="操作" fixed="right">
                            <template #default="scope">
                                <el-button type="primary" @click="$emit('downloadMaterial', scope.row, true)">
                                    下载材料
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <!-- 表与资料库已存在的数据之间的重复数据展示 -->
        <div v-if="checkFailInfo?.errorList[0].repetitionOwe" class="repeat-data-content">
            <div class="repeat-data-header">
                <div>
                    <span style="color: #1677ff">存在重复的数据表：</span>
                    <span class="template-name">{{
                        checkFailInfo?.template.templateName || "未知文件"
                    }}</span>
                </div>
            </div>

            <div
                class="repeat-data-item"
                v-for="(errorItem, index) in checkFailInfo?.errorList"
                :key="index">
                <!-- 表A区域 -->
                <div class="error-header">
                    导入文件第
                    <span style="color: #de7a5c">{{ errorItem.repetitionRow[0].rowId }}行 </span>
                    数据发现重复
                    <span class="error-toggle-btn" v-if="showMoreButtons" @click="toggleErrorItem(index)">
                        {{
                            errorItemStates[index] === 3
                                ? "展开详情"
                                : errorItemStates[index] === 2
                                  ? "收起详情"
                                  : "展开详情"
                        }}
                        <el-icon class="toggle-icon">
                            <ArrowDown
                                v-if="errorItemStates[index] === 3 || errorItemStates[index] === 1" />
                            <ArrowUp v-else />
                        </el-icon>
                    </span>
                </div>
                <el-table
                    header-row-class-name="table-header"
                    v-show="errorItemStates[index] !== 3"
                    :row-class-name="'warning-row'"
                    :data="errorItem.repetitionRow"
                    border
                    style="width: 100%">
                    <el-table-column align="center" label="状态" min-width="100">
                        <template #default="">
                            <div>
                                {{ isFromAudit ? "待审核" : "待导入" }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-for="fieldName in checkFailInfo?.templateValueList"
                        :key="fieldName.id"
                        align="center"
                        :label="fieldName.value"
                        :prop="fieldName.value">
                        <template #default="scope">
                            <FieldValueDisplay
                                :field="fieldName"
                                :row-data="scope.row"
                                :person-list="allPersonListWithDisabled"
                                :dict-list="dictList"
                                :org-list="allDependentMajors" />
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" fixed="right">
                        <template #default="scope">
                            <el-button type="primary" @click="$emit('downloadMaterial', scope.row, true)">
                                下载材料
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 表B区域 -->
                <div class="error-header" v-show="errorItemStates[index] === 2">已存在数据：</div>
                <el-table
                    v-show="errorItemStates[index] === 2"
                    :data="errorItem.repetitionOwe"
                    border
                    style="width: 100%">
                    <el-table-column align="center" label="状态" min-width="100">
                        <template #default="">
                            <div>已存在</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-for="fieldName in checkFailInfo?.templateValueList"
                        :key="fieldName.id"
                        align="center"
                        :label="fieldName.value"
                        :prop="fieldName.value">
                        <template #default="scope">
                            <FieldValueDisplay
                                :field="fieldName"
                                :row-data="scope.row"
                                :person-list="allPersonListWithDisabled"
                                :dict-list="dictList"
                                :org-list="allDependentMajors" />
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" fixed="right">
                        <template #default="scope">
                            <el-button type="primary" @click="$emit('downloadMaterial', scope.row, true)">
                                下载材料
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div class="action-buttons" v-if="!isFromAudit">
                <el-button @click="$emit('clearFailInfo')">取消导入</el-button>
                <el-button type="primary" @click="$emit('continueImport')">继续导入</el-button>
            </div>
            <div class="action-buttons" v-else>
                <el-button type="primary" @click="$emit('backToAudit')">返回审核</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import FieldValueDisplay from "@/components/FieldValueDisplay/index.vue";

interface Props {
    checkFailInfo: any;
    duplicateImportLoading: boolean;
    showMoreButtons: boolean;
    errorItemStates: { [key: number]: number };
    allPersonListWithDisabled: any[];
    dictList: any[];
    allDependentMajors: any[];
    isFromAudit: boolean;
}

interface Emits {
    (e: 'downloadMaterial', row: any, repeatModelDownload: boolean): void;
    (e: 'toggleErrorItem', index: number): void;
    (e: 'clearFailInfo'): void;
    (e: 'continueImport'): void;
    (e: 'backToAudit'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 切换展开状态
const toggleErrorItem = (index: number) => {
    emit('toggleErrorItem', index);
};
</script>

<style scoped lang="scss">
// 重复数据行hover时，不显示背景色
:deep(.warning-row:hover) {
    td {
        background-color: unset !important;
    }
}

// 表头背景色
:deep(.table-header) {
    th {
        background-color: #edf1fa !important;
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        font-weight: 500;
    }
}

// 重复数据行背景色
:deep(.el-table .warning-row) {
    --el-table-tr-bg-color: #fcf4ec;
}

.repeat-data-container {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 24px;

    .repeat-data-content {
        display: flex;
        flex-direction: column;
        gap: 32px;
        
        .template-name {
            border: 1px solid #d9d9d9;
            padding: 1px 12px;
            border-radius: 6px;
            color: rgba(0, 0, 0, 0.88);
            font-size: 14px;
            line-height: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
        }
    }

    .repeat-data-item {
        display: flex;
        padding: 16px 16px;
        flex-direction: column;
        gap: 10px;
        background: #f9fbff;
        border-radius: 8px;

        .error-header {
            height: 40px;
            line-height: 40px;
            color: #6b7995;
            font-size: 15px;
            font-weight: 600;
            border-radius: 5px;
            letter-spacing: 1.5px;
            cursor: pointer;

            .error-toggle-btn {
                color: #1677ff;
                font-size: 14px;
                font-weight: normal;
                margin-left: 8px;
                display: inline-flex;
                align-items: center;

                .toggle-icon {
                    margin-left: 4px;
                    font-size: 12px;
                    transition: transform 0.3s;
                }
            }

            .row-id {
                font-size: 16px;
                font-weight: bold;
                color: #23346d;
            }

            span:last-child {
                color: #586fbb;
            }
        }
    }

    .error-value {
        word-break: break-word;
        max-height: 100px;
        overflow-y: auto;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
    }
}
</style>