<template>
    <div class="wrapper">
        <GeneralDataDisplay>
            <template #top>
                <div class="top">
                    <div>
                        <el-input
                            style="width: 420px; margin-right: 10px"
                            size="large"
                            placeholder="输入 数据表名称、项目名称、参与人 检索待审核内容"
                            v-model="searchKeyword">
                            <template #append>
                                <el-button type="primary" :icon="Search" @click="handleSearch" />
                            </template>
                        </el-input>
                    </div>
                </div>
            </template>

            <template #content>
                <div class="content">
                    <DataTable
                        @loadComplete="handleLoadComplete"
                        ref="dataTableRef"
                        :is-load-first="isAutoLoadFirst"
                        :search-keyword="searchKeyword"
                        :is-local-search="false"
                        @itemClick="loadDetail"
                        :load-more-func="loadMoreFunc">
                        <template #title="{ item }: { item: CmsCheck0 }">
                            <!-- 主页点击跳转后，地址栏中的id会与此处的id对应： -->
                            <!-- {{ item.id }} -->
                            <div class="title">
                                {{ getTemplateNameById(item.row?.categoryTemplateId) || "未知模板" }}
                            </div>
                        </template>
                        <template #desc="{ item }: { item: CmsCheck0 }">
                            <div>创建日期：{{ item.createdAt }}</div>
                            <div>项目名称：{{ item.projectName }}</div>
                        </template>
                        <template #detail="{ selectedItem }">
                            <Detail @refresh="refreshAll" :selected-item="detailData" :detailLoading="detailLoading" />
                        </template>
                    </DataTable>
                </div>
            </template>
        </GeneralDataDisplay>
    </div>
</template>

<script setup lang="ts">
import GeneralDataDisplay from "@/components/GeneralDataDisplay/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import Detail from "./detail.vue";
import { Search } from "@element-plus/icons-vue";
import { CheckStatus } from "@/enums/approval/checkStatus";
import { cmsCategoryTemplateValueGetValueByTemplateId } from "@/apis/cmsCategoryTemplateValueController";
import { cmsCategoryRowGetById } from "@/apis/cmsCategoryRowController";
import { cmsCheckGetByStatus } from "@/apis/cmsCheckController";
import { CheckTypeEnum } from "@/enums/check/CheckType";
import { CmsCategoryRow0, CmsCategoryTemplateValue10, CmsCheck0 } from "@/apis";
import { useCategoryTemplateList } from "@/hooks/useCategoryTemplate";

const route = useRoute();

// 存储当前选中项详情
const detailData = ref<
    | (CmsCategoryRow0 & {
          isPrincipal?: number;
          fieldsList?: CmsCategoryTemplateValue10[];
          isRepeat?: boolean;
          isRead?: number;
      })
    | null
>(null);
// 搜索关键字
const searchKeyword = ref("");
// 存储当前选中项的数据项列表
const fieldsList = ref([]);
// 存储dataTable组件实例
const dataTableRef = ref();

const { getTemplateNameById } = useCategoryTemplateList();
// 详情加载中
const detailLoading = ref(false);

// 获取详情
const loadDetail = (item) => {
    detailLoading.value = true;

    Promise.all([
        cmsCategoryTemplateValueGetValueByTemplateId({ params: { id: item.row.categoryTemplateId } }),
        cmsCategoryRowGetById({ params: { id: item.row.id } }),
    ]).then(([res1, res2]) => {
        fieldsList.value = res1.data;
        detailData.value = res2.data;
        detailData.value.isPrincipal = item.isPrincipal;
        detailData.value.fieldsList = fieldsList.value;
        detailData.value.isRepeat = item.isRepeat;
        detailData.value.isRead = item.isRead;
        detailLoading.value = false;
    });
};

// 处理搜索
const handleSearch = () => {
    if (dataTableRef.value) {
        dataTableRef.value.search();
    }
};

// 刷新待审核列表
const refreshAll = () => {
    if (dataTableRef.value) {
        dataTableRef.value.refreshAll();
        detailData.value = null;
    }
};

const loadMoreFunc = (pageNum: number, pageSize: number, searchKeyword?: string) => {
    return cmsCheckGetByStatus({
        params: {
            pageNum,
            pageSize,
            status: [CheckStatus.WAIT_CHECK],
            types: [CheckTypeEnum.NORMAL],
            name: searchKeyword.trim(),
        },
    });
};

const isAutoLoadFirst = computed(() => {
    return !(route.query.queryListItemId && route.query.queryIndex);
});

// type: 审核待办：0，录入待办-待补充：1，录入待办-审核退回：2，项目团队待办：3
// 数据加载完成后的处理
const handleLoadComplete = (data) => {
    const queryIndex = Number(route.query.queryIndex);
    // 如果有索引参数，模拟点击对应索引的项目
    if (queryIndex !== undefined && queryIndex !== null ) {
        // 延迟一点执行，确保列表已经完全渲染
        nextTick(() => {
            dataTableRef.value.simulateClick(Number(queryIndex));
        });
    }
};
</script>

<style scoped lang="scss">
.wrapper {
    .top {
        padding: 16px 32px;
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #ffffff;
        border-radius: 6px;
    }

    .content {
        .title {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
