<template>
    <div class="collage-statistics-wrapper">
        <div v-if="!showDetail" class="collage-statistics-header-wrapper">
            <div class="collage-statistics-header">
                <div class="collage-statistics-header-left">
                    选择时间：
                    <el-date-picker
                        v-model="date"
                        type="year"
                        placeholder="请选择年份"
                        format="YYYY年度"
                        value-format="YYYY" />
                </div>
                <div>
                    <el-button
                        v-if="isExport"
                        plain
                        @click="
                            multipleTableRef.clearSelection;
                            isExport = false;
                        "
                        >取消选择</el-button
                    >
                    <el-button
                        :disabled="tableData.length === 0"
                        v-if="!isExport"
                        type="primary"
                        @click="isExport = !isExport"
                        >导出绩效</el-button
                    >
                    <el-button :disabled="multipleSelection.length === 0" v-else type="primary" @click="exportData"
                        >导出选中数据</el-button
                    >
                </div>
            </div>
            <div class="collage-statistics-content" v-loading="loading">
                <div class="table-box">
                    <el-table
                        ref="multipleTableRef"
                        border
                        :data="tableData"
                        style="width: 100%"
                        :header-cell-style="{
                            background: '#EDF1FA',
                            color: 'rgba(0, 0, 0, 0.88)',
                            fontWeight: '500',
                            fontSize: '14px',
                        }"
                        :header-row-style="{ height: '47px' }"
                        :row-style="{ height: '72px' }"
                        @selection-change="handleSelectionChange"
                        height="100%">
                        <el-table-column fixed type="selection" width="55" label="全选" v-if="isExport" />
                        <el-table-column label="序号" type="index" width="80" />
                        <el-table-column label="姓名" prop="personName" width="320" />
                        <el-table-column label="得分" prop="totalScore" />
                        <el-table-column label="操作" prop="name" width="200">
                            <template #default="scope">
                                <div class="custom-round-button" @click="goDetail(scope.row)">查看详情</div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <!-- 个人绩效详情组件 -->
        <MyPerformanceScore
            v-if="showDetail"
            :person-id="selectedPersonId"
            :custom-back="handleBack"
            @back="handleBack" />
    </div>
</template>

<script setup lang="ts">
import { pmsScoreGetAllScoreListByYear } from "@/apis/pmsScoreController";
import { commonDownloadFile } from "@/utils/fileDownload";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import MyPerformanceScore from "@/components/MyPerformanceScore/index.vue";

const router = useRouter();

const date = ref<string>("2025");

const isExport = ref<boolean>(false);

const multipleTableRef = ref<any>(null);

const multipleSelection = ref<any[]>([]);

const tableData = ref<any[]>([]);

const loading = ref<boolean>(false);

// 是否显示详情
const showDetail = ref<boolean>(false);

// 当前选中的人员ID
const selectedPersonId = ref<number | null>(null);

// 处理选择数据
const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
};

// 导出选中数据
const exportData = async () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.warning("请选择要导出的数据");
        return;
    }

    try {
        await commonDownloadFile("/pms/pmsScore/export/getAllScoreListByYear", {
            personIds: multipleSelection.value.map((item) => item.personId),
            year: Number(date.value),
        });

        // 重置选择状态
        multipleSelection.value = [];
        multipleTableRef.value!.clearSelection();
        isExport.value = false;
    } catch (error) {
        console.error("导出操作失败:", error);
    }
};

// 查看详情
const goDetail = (row: any) => {
    selectedPersonId.value = row.personId;
    showDetail.value = true;
};

// 返回列表
const handleBack = () => {
    showDetail.value = false;
    selectedPersonId.value = null;
};

watch(date, () => {
    getTableData();
});

onMounted(() => {
    getTableData();
});

const getTableData = () => {
    loading.value = true;
    pmsScoreGetAllScoreListByYear({
        params: {
            year: Number(date.value),
        },
    })
        .then((res) => {
            tableData.value = res.data;
        })
        .finally(() => {
            loading.value = false;
        });
};
</script>

<style scoped lang="scss">
.collage-statistics-wrapper {
    height: 100%;
    width: 100%;
    /* height: calc(100vh - 120px); */
    .collage-statistics-header-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 12px;
        .collage-statistics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 32px;

            background: #ffffff;
            border-radius: 6px;

            .collage-statistics-header-left {
                color: #1677ff;
                font-family: "Alibaba PuHuiTi 3.0";
                font-size: 14px;
            }
        }
        .collage-statistics-content {
            flex: 1;
            height: 0;
            overflow: auto;
            background-color: #fff;

            .table-box {
                height: 100%;
                position: relative;
                :deep(.el-table) {
                    position: absolute;
                }

                .custom-round-button {
                    color: #1677ff;
                    font-family: "Alibaba PuHuiTi 3.0";
                    font-size: 14px;
                    font-style: normal;
                    line-height: 22px; /* 1.571 */
                    cursor: pointer;
                }

                ::v-deep(.el-table__cell .cell) {
                    padding-left: 16px; /* 或者设置为 0 移除左边距 */
                }
            }
        }
    }
}
</style>
